<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - CRM</title>
    <base target="_top">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>
        .customer-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .customer-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .customer-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        
        .search-box {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            height: 100%;
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }
        
        .table-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .pagination {
            justify-content: center;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 mb-0">جاري تحميل البيانات...</p>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-users me-2"></i>إدارة العملاء</h2>
                <p class="text-muted mb-0">إدارة بيانات العملاء والعلاقات</p>
            </div>
            <div>
                <button class="btn btn-primary" onclick="showAddCustomerModal()">
                    <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                </button>
                <div class="btn-group ms-2">
                    <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-cog me-2"></i>المزيد
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportCustomers()">
                            <i class="fas fa-download me-2"></i>تصدير البيانات
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showImportModal()">
                            <i class="fas fa-upload me-2"></i>استيراد البيانات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="findDuplicates()">
                            <i class="fas fa-search me-2"></i>البحث عن مكررات
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4" id="statsCards">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon bg-primary">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4 class="mb-1" id="totalCustomers">-</h4>
                    <p class="text-muted mb-0">إجمالي العملاء</p>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon bg-success">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <h4 class="mb-1" id="activeCustomers">-</h4>
                    <p class="text-muted mb-0">العملاء النشطين</p>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon bg-info">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h4 class="mb-1" id="newCustomersMonth">-</h4>
                    <p class="text-muted mb-0">جدد هذا الشهر</p>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon bg-warning">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <h4 class="mb-1" id="newCustomersWeek">-</h4>
                    <p class="text-muted mb-0">جدد هذا الأسبوع</p>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="search-box">
            <form id="searchForm" onsubmit="searchCustomers(event)">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" 
                                   placeholder="البحث في الاسم، البريد، الشركة...">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter" onchange="applyFilters()">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="text" class="form-control" id="companyFilter" 
                               placeholder="الشركة" onchange="applyFilters()">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="sortBy" onchange="applyFilters()">
                            <option value="name">ترتيب بالاسم</option>
                            <option value="created_at">ترتيب بالتاريخ</option>
                            <option value="company">ترتيب بالشركة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                            <i class="fas fa-times me-2"></i>مسح
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Customers Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">قائمة العملاء</h5>
                <div class="d-flex align-items-center">
                    <span class="text-muted me-3" id="resultsCount">0 عميل</span>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary active" onclick="setViewMode('table')" id="tableViewBtn">
                            <i class="fas fa-table"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="setViewMode('cards')" id="cardsViewBtn">
                            <i class="fas fa-th-large"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- Table View -->
                <div id="tableView">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>العميل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الشركة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                <!-- سيتم تحميل البيانات ديناميكياً -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Cards View -->
                <div id="cardsView" style="display: none;">
                    <div class="row p-3" id="customersCardsContainer">
                        <!-- سيتم تحميل البطاقات ديناميكياً -->
                    </div>
                </div>

                <!-- Empty State -->
                <div id="emptyState" class="text-center py-5" style="display: none;">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد عملاء</h5>
                    <p class="text-muted">ابدأ بإضافة عميل جديد</p>
                    <button class="btn btn-primary" onclick="showAddCustomerModal()">
                        <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <nav aria-label="تصفح الصفحات" class="mt-4">
            <ul class="pagination" id="pagination">
                <!-- سيتم إنشاء أزرار التصفح ديناميكياً -->
            </ul>
        </nav>
    </div>

    <!-- Add/Edit Customer Modal -->
    <div class="modal fade" id="customerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="customerModalTitle">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="customerForm">
                        <input type="hidden" id="customerId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customerName" class="form-label">اسم العميل *</label>
                                    <input type="text" class="form-control" id="customerName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customerEmail" class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" id="customerEmail" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customerPhone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="customerPhone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customerCompany" class="form-label">الشركة</label>
                                    <input type="text" class="form-control" id="customerCompany">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="customerAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="customerAddress" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="customerNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="customerNotes" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="customerStatus" class="form-label">الحالة</label>
                            <select class="form-select" id="customerStatus">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveCustomer()">
                        <i class="fas fa-save me-2"></i>حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">استيراد العملاء</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="csvFile" class="form-label">اختر ملف CSV</label>
                        <input type="file" class="form-control" id="csvFile" accept=".csv">
                        <div class="form-text">
                            يجب أن يحتوي الملف على الأعمدة: الاسم، البريد الإلكتروني، الهاتف، الشركة، العنوان
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        يمكنك تحميل نموذج ملف CSV من 
                        <a href="#" onclick="downloadTemplate()">هنا</a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="importCustomers()">
                        <i class="fas fa-upload me-2"></i>استيراد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Global Variables
        let currentPage = 1;
        let itemsPerPage = 25;
        let totalPages = 1;
        let currentViewMode = 'table';
        let currentFilters = {};
        let customers = [];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadCustomerStats();
            loadCustomers();
        });

        // Load customer statistics
        function loadCustomerStats() {
            showLoading();
            
            google.script.run
                .withSuccessHandler(handleStatsLoaded)
                .withFailureHandler(handleError)
                .handleAjaxRequest('getCustomerStats', 'CRM');
        }

        // Handle stats loaded
        function handleStatsLoaded(result) {
            hideLoading();
            
            if (result.success) {
                const stats = result.data;
                document.getElementById('totalCustomers').textContent = stats.total || 0;
                document.getElementById('activeCustomers').textContent = stats.active || 0;
                document.getElementById('newCustomersMonth').textContent = stats.thisMonth || 0;
                document.getElementById('newCustomersWeek').textContent = stats.thisWeek || 0;
            }
        }

        // Load customers
        function loadCustomers(page = 1) {
            currentPage = page;
            showLoading();
            
            const params = {
                ...currentFilters,
                limit: itemsPerPage,
                offset: (page - 1) * itemsPerPage
            };
            
            google.script.run
                .withSuccessHandler(handleCustomersLoaded)
                .withFailureHandler(handleError)
                .handleAjaxRequest('list', 'CRM', params);
        }

        // Handle customers loaded
        function handleCustomersLoaded(result) {
            hideLoading();
            
            if (result.success) {
                customers = result.data;
                totalPages = result.totalPages;
                
                updateResultsCount(result.total);
                
                if (customers.length === 0) {
                    showEmptyState();
                } else {
                    hideEmptyState();
                    if (currentViewMode === 'table') {
                        renderCustomersTable();
                    } else {
                        renderCustomersCards();
                    }
                }
                
                renderPagination();
            } else {
                showError(result.error);
            }
        }

        // Render customers table
        function renderCustomersTable() {
            const tbody = document.getElementById('customersTableBody');
            tbody.innerHTML = '';
            
            customers.forEach(customer => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="customer-avatar me-3">
                                ${customer.name.charAt(0).toUpperCase()}
                            </div>
                            <div>
                                <div class="fw-bold">${customer.name}</div>
                                <small class="text-muted">${customer.customer_code || ''}</small>
                            </div>
                        </div>
                    </td>
                    <td>${customer.email || ''}</td>
                    <td>${customer.phone || ''}</td>
                    <td>${customer.company || ''}</td>
                    <td>
                        <span class="badge ${customer.status === 'active' ? 'bg-success' : 'bg-secondary'} status-badge">
                            ${customer.status_text}
                        </span>
                    </td>
                    <td>${customer.created_at_formatted || ''}</td>
                    <td>
                        <div class="table-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewCustomer('${customer.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="editCustomer('${customer.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="toggleCustomerStatus('${customer.id}')" title="تغيير الحالة">
                                <i class="fas fa-toggle-${customer.status === 'active' ? 'on' : 'off'}"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCustomer('${customer.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Render customers cards
        function renderCustomersCards() {
            const container = document.getElementById('customersCardsContainer');
            container.innerHTML = '';
            
            customers.forEach(customer => {
                const card = document.createElement('div');
                card.className = 'col-md-6 col-lg-4 mb-3';
                card.innerHTML = `
                    <div class="card customer-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-start mb-3">
                                <div class="customer-avatar me-3">
                                    ${customer.name.charAt(0).toUpperCase()}
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="card-title mb-1">${customer.name}</h6>
                                    <small class="text-muted">${customer.customer_code || ''}</small>
                                </div>
                                <span class="badge ${customer.status === 'active' ? 'bg-success' : 'bg-secondary'} status-badge">
                                    ${customer.status_text}
                                </span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted d-block">
                                    <i class="fas fa-envelope me-1"></i>${customer.email || 'غير محدد'}
                                </small>
                                <small class="text-muted d-block">
                                    <i class="fas fa-phone me-1"></i>${customer.phone || 'غير محدد'}
                                </small>
                                <small class="text-muted d-block">
                                    <i class="fas fa-building me-1"></i>${customer.company || 'غير محدد'}
                                </small>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">${customer.created_at_formatted || ''}</small>
                                <div class="table-actions">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewCustomer('${customer.id}')" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="editCustomer('${customer.id}')" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteCustomer('${customer.id}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        // Show/Hide empty state
        function showEmptyState() {
            document.getElementById('tableView').style.display = 'none';
            document.getElementById('cardsView').style.display = 'none';
            document.getElementById('emptyState').style.display = 'block';
        }

        function hideEmptyState() {
            document.getElementById('emptyState').style.display = 'none';
            if (currentViewMode === 'table') {
                document.getElementById('tableView').style.display = 'block';
                document.getElementById('cardsView').style.display = 'none';
            } else {
                document.getElementById('tableView').style.display = 'none';
                document.getElementById('cardsView').style.display = 'block';
            }
        }

        // Set view mode
        function setViewMode(mode) {
            currentViewMode = mode;
            
            document.getElementById('tableViewBtn').classList.toggle('active', mode === 'table');
            document.getElementById('cardsViewBtn').classList.toggle('active', mode === 'cards');
            
            if (customers.length > 0) {
                hideEmptyState();
            }
        }

        // Update results count
        function updateResultsCount(total) {
            document.getElementById('resultsCount').textContent = `${total} عميل`;
        }

        // Search customers
        function searchCustomers(event) {
            event.preventDefault();
            
            const searchTerm = document.getElementById('searchInput').value.trim();
            currentFilters.search = searchTerm;
            
            loadCustomers(1);
        }

        // Apply filters
        function applyFilters() {
            currentFilters = {
                search: document.getElementById('searchInput').value.trim(),
                status: document.getElementById('statusFilter').value,
                company: document.getElementById('companyFilter').value.trim(),
                orderBy: document.getElementById('sortBy').value
            };
            
            loadCustomers(1);
        }

        // Clear filters
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('companyFilter').value = '';
            document.getElementById('sortBy').value = 'name';
            
            currentFilters = {};
            loadCustomers(1);
        }

        // Show add customer modal
        function showAddCustomerModal() {
            document.getElementById('customerModalTitle').textContent = 'إضافة عميل جديد';
            document.getElementById('customerForm').reset();
            document.getElementById('customerId').value = '';
            
            const modal = new bootstrap.Modal(document.getElementById('customerModal'));
            modal.show();
        }

        // Edit customer
        function editCustomer(id) {
            const customer = customers.find(c => c.id === id);
            if (!customer) return;
            
            document.getElementById('customerModalTitle').textContent = 'تعديل بيانات العميل';
            document.getElementById('customerId').value = customer.id;
            document.getElementById('customerName').value = customer.name || '';
            document.getElementById('customerEmail').value = customer.email || '';
            document.getElementById('customerPhone').value = customer.phone || '';
            document.getElementById('customerCompany').value = customer.company || '';
            document.getElementById('customerAddress').value = customer.address || '';
            document.getElementById('customerNotes').value = customer.notes || '';
            document.getElementById('customerStatus').value = customer.status || 'active';
            
            const modal = new bootstrap.Modal(document.getElementById('customerModal'));
            modal.show();
        }

        // Save customer
        function saveCustomer() {
            const form = document.getElementById('customerForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            const customerId = document.getElementById('customerId').value;
            const customerData = {
                name: document.getElementById('customerName').value.trim(),
                email: document.getElementById('customerEmail').value.trim(),
                phone: document.getElementById('customerPhone').value.trim(),
                company: document.getElementById('customerCompany').value.trim(),
                address: document.getElementById('customerAddress').value.trim(),
                notes: document.getElementById('customerNotes').value.trim(),
                status: document.getElementById('customerStatus').value
            };
            
            showLoading();
            
            const action = customerId ? 'update' : 'create';
            const params = customerId ? { id: customerId, ...customerData } : customerData;
            
            google.script.run
                .withSuccessHandler(handleCustomerSaved)
                .withFailureHandler(handleError)
                .handleAjaxRequest(action, 'CRM', params);
        }

        // Handle customer saved
        function handleCustomerSaved(result) {
            hideLoading();
            
            if (result.success) {
                const modal = bootstrap.Modal.getInstance(document.getElementById('customerModal'));
                modal.hide();
                
                showSuccess('تم حفظ بيانات العميل بنجاح');
                loadCustomers(currentPage);
                loadCustomerStats();
            } else {
                showError(result.error || result.errors?.join(', '));
            }
        }

        // Delete customer
        function deleteCustomer(id) {
            const customer = customers.find(c => c.id === id);
            if (!customer) return;
            
            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف العميل "${customer.name}"؟`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    showLoading();
                    
                    google.script.run
                        .withSuccessHandler(handleCustomerDeleted)
                        .withFailureHandler(handleError)
                        .handleAjaxRequest('delete', 'CRM', { id: id });
                }
            });
        }

        // Handle customer deleted
        function handleCustomerDeleted(result) {
            hideLoading();
            
            if (result.success) {
                showSuccess('تم حذف العميل بنجاح');
                loadCustomers(currentPage);
                loadCustomerStats();
            } else {
                showError(result.error);
            }
        }

        // Toggle customer status
        function toggleCustomerStatus(id) {
            showLoading();
            
            google.script.run
                .withSuccessHandler(handleStatusToggled)
                .withFailureHandler(handleError)
                .handleAjaxRequest('toggleCustomerStatus', 'CRM', { id: id });
        }

        // Handle status toggled
        function handleStatusToggled(result) {
            hideLoading();
            
            if (result.success) {
                showSuccess('تم تغيير حالة العميل بنجاح');
                loadCustomers(currentPage);
                loadCustomerStats();
            } else {
                showError(result.error);
            }
        }

        // View customer details
        function viewCustomer(id) {
            const customer = customers.find(c => c.id === id);
            if (!customer) return;
            
            Swal.fire({
                title: customer.name,
                html: `
                    <div class="text-start">
                        <p><strong>كود العميل:</strong> ${customer.customer_code || 'غير محدد'}</p>
                        <p><strong>البريد الإلكتروني:</strong> ${customer.email || 'غير محدد'}</p>
                        <p><strong>الهاتف:</strong> ${customer.phone || 'غير محدد'}</p>
                        <p><strong>الشركة:</strong> ${customer.company || 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> ${customer.address || 'غير محدد'}</p>
                        <p><strong>الحالة:</strong> ${customer.status_text}</p>
                        <p><strong>تاريخ التسجيل:</strong> ${customer.created_at_formatted || 'غير محدد'}</p>
                        ${customer.notes ? `<p><strong>ملاحظات:</strong> ${customer.notes}</p>` : ''}
                    </div>
                `,
                confirmButtonText: 'موافق',
                width: '600px'
            });
        }

        // Export customers
        function exportCustomers() {
            showLoading();
            
            google.script.run
                .withSuccessHandler(handleExportCompleted)
                .withFailureHandler(handleError)
                .handleAjaxRequest('export', 'CRM', { format: 'csv', ...currentFilters });
        }

        // Handle export completed
        function handleExportCompleted(result) {
            hideLoading();
            
            if (result.success) {
                showSuccess('تم تصدير البيانات بنجاح');
                // في التطبيق الحقيقي، سيتم تحميل الملف
            } else {
                showError(result.error);
            }
        }

        // Show import modal
        function showImportModal() {
            const modal = new bootstrap.Modal(document.getElementById('importModal'));
            modal.show();
        }

        // Import customers
        function importCustomers() {
            const fileInput = document.getElementById('csvFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showError('يرجى اختيار ملف CSV');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const csvData = e.target.result;
                
                showLoading();
                
                google.script.run
                    .withSuccessHandler(handleImportCompleted)
                    .withFailureHandler(handleError)
                    .handleAjaxRequest('importCustomers', 'CRM', { csvData: csvData });
            };
            
            reader.readAsText(file);
        }

        // Handle import completed
        function handleImportCompleted(result) {
            hideLoading();
            
            if (result.success) {
                const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
                modal.hide();
                
                const data = result.data;
                showSuccess(`تم استيراد ${data.success} عميل من أصل ${data.total}`);
                
                if (data.failed > 0) {
                    console.log('أخطاء الاستيراد:', data.errors);
                }
                
                loadCustomers(1);
                loadCustomerStats();
            } else {
                showError(result.error);
            }
        }

        // Download template
        function downloadTemplate() {
            const csvContent = 'الاسم,البريد الإلكتروني,الهاتف,الشركة,العنوان,الحالة\n' +
                              'عميل تجريبي,<EMAIL>,01234567890,شركة تجريبية,عنوان تجريبي,active';
            
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'customers_template.csv';
            link.click();
        }

        // Find duplicates
        function findDuplicates() {
            showLoading();
            
            google.script.run
                .withSuccessHandler(handleDuplicatesFound)
                .withFailureHandler(handleError)
                .handleAjaxRequest('findDuplicateCustomers', 'CRM');
        }

        // Handle duplicates found
        function handleDuplicatesFound(result) {
            hideLoading();
            
            if (result.success) {
                const duplicates = result.data;
                
                if (duplicates.length === 0) {
                    showSuccess('لم يتم العثور على عملاء مكررين');
                } else {
                    let html = '<div class="text-start">';
                    duplicates.forEach(duplicate => {
                        html += `<p><strong>${duplicate.type === 'email' ? 'بريد إلكتروني' : 'هاتف'} مكرر:</strong> ${duplicate.value}</p>`;
                        html += '<ul>';
                        duplicate.customers.forEach(customer => {
                            html += `<li>${customer.name} (${customer.id})</li>`;
                        });
                        html += '</ul>';
                    });
                    html += '</div>';
                    
                    Swal.fire({
                        title: 'عملاء مكررون',
                        html: html,
                        confirmButtonText: 'موافق',
                        width: '600px'
                    });
                }
            } else {
                showError(result.error);
            }
        }

        // Render pagination
        function renderPagination() {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            if (totalPages <= 1) return;
            
            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadCustomers(${currentPage - 1})">السابق</a>`;
            pagination.appendChild(prevLi);
            
            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="loadCustomers(${i})">${i}</a>`;
                pagination.appendChild(li);
            }
            
            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadCustomers(${currentPage + 1})">التالي</a>`;
            pagination.appendChild(nextLi);
        }

        // Utility functions
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showSuccess(message) {
            Swal.fire({
                title: 'نجح',
                text: message,
                icon: 'success',
                timer: 3000,
                showConfirmButton: false
            });
        }

        function showError(message) {
            Swal.fire({
                title: 'خطأ',
                text: message,
                icon: 'error',
                confirmButtonText: 'موافق'
            });
        }

        function handleError(error) {
            console.error('Error:', error);
            hideLoading();
            showError('حدث خطأ في النظام، يرجى المحاولة مرة أخرى');
        }
    </script>
</body>
</html>
