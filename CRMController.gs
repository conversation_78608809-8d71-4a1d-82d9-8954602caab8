/**
 * متحكم إدارة العملاء (CRM Controller)
 * CRMController.gs
 * يدير جميع العمليات والطلبات المتعلقة بإدارة العملاء
 */

/**
 * فئة متحكم CRM
 */
class CRMController extends BaseController {
  constructor() {
    super(CRMModel, 'CRM');
  }

  /**
   * الحصول على حقول البحث
   */
  getSearchFields() {
    return ['name', 'email', 'company', 'phone'];
  }

  /**
   * بناء معايير البحث المخصصة
   */
  buildSearchCriteria(params) {
    const criteria = super.buildSearchCriteria(params);
    
    // البحث المتقدم في CRM
    if (params.customerCode) {
      criteria.customer_code = params.customerCode;
    }
    
    if (params.company) {
      criteria.company = { $like: params.company };
    }
    
    if (params.city) {
      criteria.address = { $like: params.city };
    }
    
    return criteria;
  }

  /**
   * البحث المتقدم في العملاء
   */
  searchCustomers(params = {}) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('read')) {
        return { success: false, error: 'ليس لديك صلاحية لقراءة بيانات العملاء' };
      }

      const searchTerm = params.search || '';
      const filters = {
        status: params.status,
        company: params.company,
        city: params.city,
        dateFrom: params.dateFrom,
        dateTo: params.dateTo,
        orderBy: params.orderBy || 'name',
        orderDirection: params.orderDirection || 'asc',
        limit: parseInt(params.limit) || CONFIG.UI.ITEMS_PER_PAGE,
        offset: parseInt(params.offset) || 0
      };

      const result = this.model.searchCustomers(searchTerm, filters);
      
      if (result.data) {
        result.data = result.data.map(customer => this.afterRead(customer));
      }

      return {
        success: true,
        data: result.data,
        total: result.total,
        page: Math.floor(filters.offset / filters.limit) + 1,
        totalPages: Math.ceil(result.total / filters.limit),
        limit: filters.limit
      };
    } catch (error) {
      return this.handleError(error, 'searchCustomers');
    }
  }

  /**
   * الحصول على العملاء النشطين
   */
  getActiveCustomers() {
    try {
      if (!this.hasPermission('read')) {
        return { success: false, error: 'ليس لديك صلاحية لقراءة بيانات العملاء' };
      }

      const result = this.model.getActiveCustomers();
      
      return {
        success: true,
        data: result.data.map(customer => this.afterRead(customer))
      };
    } catch (error) {
      return this.handleError(error, 'getActiveCustomers');
    }
  }

  /**
   * الحصول على العملاء الجدد
   */
  getNewCustomers(days = 30) {
    try {
      if (!this.hasPermission('read')) {
        return { success: false, error: 'ليس لديك صلاحية لقراءة بيانات العملاء' };
      }

      const result = this.model.getNewCustomers(days);
      
      return {
        success: true,
        data: result.data.map(customer => this.afterRead(customer))
      };
    } catch (error) {
      return this.handleError(error, 'getNewCustomers');
    }
  }

  /**
   * الحصول على أفضل العملاء
   */
  getTopCustomers(limit = 10) {
    try {
      if (!this.hasPermission('read')) {
        return { success: false, error: 'ليس لديك صلاحية لقراءة بيانات العملاء' };
      }

      const result = this.model.getTopCustomers(limit);
      
      return {
        success: true,
        data: result.data.map(customer => this.afterRead(customer))
      };
    } catch (error) {
      return this.handleError(error, 'getTopCustomers');
    }
  }

  /**
   * الحصول على إحصائيات العملاء
   */
  getCustomerStats() {
    try {
      if (!this.hasPermission('read')) {
        return { success: false, error: 'ليس لديك صلاحية لقراءة بيانات العملاء' };
      }

      return this.model.getCustomerStats();
    } catch (error) {
      return this.handleError(error, 'getCustomerStats');
    }
  }

  /**
   * الحصول على الإحصائيات المخصصة
   */
  getCustomStats(params) {
    try {
      const stats = {};
      
      // إحصائيات حسب الشركة
      const companiesResult = this.model.find({}, { 
        orderBy: 'company',
        orderDirection: 'asc' 
      });
      
      const companiesCount = {};
      companiesResult.data.forEach(customer => {
        const company = customer.company || 'غير محدد';
        companiesCount[company] = (companiesCount[company] || 0) + 1;
      });
      
      stats.byCompany = Object.keys(companiesCount).map(company => ({
        name: company,
        count: companiesCount[company]
      })).sort((a, b) => b.count - a.count).slice(0, 10);

      // إحصائيات حسب الحالة
      stats.byStatus = {
        active: this.model.count({ status: 'active' }),
        inactive: this.model.count({ status: 'inactive' })
      };

      // إحصائيات شهرية (آخر 6 أشهر)
      stats.monthlyRegistrations = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);
        
        const count = this.model.count({
          created_at: {
            $gt: monthStart,
            $lt: monthEnd
          }
        });
        
        stats.monthlyRegistrations.push({
          month: date.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long' }),
          count: count
        });
      }

      return stats;
    } catch (error) {
      Logger.log('خطأ في getCustomStats: ' + error.toString());
      return {};
    }
  }

  /**
   * تنسيق البيانات للتصدير
   */
  formatForExport(data) {
    return data.map(customer => ({
      'كود العميل': customer.customer_code || '',
      'الاسم': customer.name || '',
      'البريد الإلكتروني': customer.email || '',
      'الهاتف': customer.phone || '',
      'الشركة': customer.company || '',
      'العنوان': customer.address || '',
      'الملاحظات': customer.notes || '',
      'الحالة': customer.status === 'active' ? 'نشط' : 'غير نشط',
      'تاريخ التسجيل': customer.registration_date ? 
        new Date(customer.registration_date).toLocaleDateString('ar-EG') : '',
      'تاريخ الإنشاء': customer.created_at ? 
        new Date(customer.created_at).toLocaleDateString('ar-EG') : '',
      'آخر تحديث': customer.updated_at ? 
        new Date(customer.updated_at).toLocaleDateString('ar-EG') : ''
    }));
  }

  /**
   * استيراد العملاء
   */
  importCustomers(csvData) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('import')) {
        return { success: false, error: 'ليس لديك صلاحية لاستيراد البيانات' };
      }

      const result = this.model.importFromCSV(csvData);
      
      if (result.success) {
        // إرسال إشعار بنتيجة الاستيراد
        this.sendNotification('import', {
          total: result.data.total,
          success: result.data.success,
          failed: result.data.failed
        });
      }

      return result;
    } catch (error) {
      return this.handleError(error, 'importCustomers');
    }
  }

  /**
   * تفعيل/إلغاء تفعيل عميل
   */
  toggleCustomerStatus(id) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('update')) {
        return { success: false, error: 'ليس لديك صلاحية لتحديث بيانات العملاء' };
      }

      const customer = this.model.findById(id);
      if (!customer) {
        return { success: false, error: 'العميل غير موجود' };
      }

      const newStatus = customer.status === 'active' ? 'inactive' : 'active';
      
      return this.update(id, { status: newStatus });
    } catch (error) {
      return this.handleError(error, 'toggleCustomerStatus');
    }
  }

  /**
   * دمج عملاء مكررين
   */
  mergeCustomers(primaryId, secondaryId) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('update') || !this.hasPermission('delete')) {
        return { success: false, error: 'ليس لديك صلاحية لدمج العملاء' };
      }

      const primaryCustomer = this.model.findById(primaryId);
      const secondaryCustomer = this.model.findById(secondaryId);

      if (!primaryCustomer || !secondaryCustomer) {
        return { success: false, error: 'أحد العملاء غير موجود' };
      }

      // دمج البيانات (الاحتفاظ بالبيانات الأكثر اكتمالاً)
      const mergedData = {
        name: primaryCustomer.name || secondaryCustomer.name,
        email: primaryCustomer.email || secondaryCustomer.email,
        phone: primaryCustomer.phone || secondaryCustomer.phone,
        company: primaryCustomer.company || secondaryCustomer.company,
        address: primaryCustomer.address || secondaryCustomer.address,
        notes: [primaryCustomer.notes, secondaryCustomer.notes]
          .filter(note => note && note.trim())
          .join('\n---\n')
      };

      // تحديث العميل الأساسي
      const updateResult = this.update(primaryId, mergedData);
      
      if (updateResult.success) {
        // حذف العميل الثانوي
        const deleteResult = this.delete(secondaryId);
        
        if (deleteResult.success) {
          return {
            success: true,
            message: 'تم دمج العملاء بنجاح',
            mergedCustomer: updateResult.data
          };
        } else {
          return { success: false, error: 'فشل في حذف العميل الثانوي' };
        }
      } else {
        return { success: false, error: 'فشل في تحديث العميل الأساسي' };
      }
    } catch (error) {
      return this.handleError(error, 'mergeCustomers');
    }
  }

  /**
   * البحث عن عملاء مكررين
   */
  findDuplicateCustomers() {
    try {
      if (!this.hasPermission('read')) {
        return { success: false, error: 'ليس لديك صلاحية لقراءة بيانات العملاء' };
      }

      const allCustomers = this.model.all().data;
      const duplicates = [];
      const emailMap = {};
      const phoneMap = {};

      // البحث عن تكرار في البريد الإلكتروني
      allCustomers.forEach(customer => {
        if (customer.email) {
          const email = customer.email.toLowerCase();
          if (emailMap[email]) {
            duplicates.push({
              type: 'email',
              value: email,
              customers: [emailMap[email], customer]
            });
          } else {
            emailMap[email] = customer;
          }
        }
      });

      // البحث عن تكرار في رقم الهاتف
      allCustomers.forEach(customer => {
        if (customer.phone) {
          const phone = customer.phone.replace(/\s+/g, '');
          if (phoneMap[phone]) {
            duplicates.push({
              type: 'phone',
              value: phone,
              customers: [phoneMap[phone], customer]
            });
          } else {
            phoneMap[phone] = customer;
          }
        }
      });

      return {
        success: true,
        data: duplicates
      };
    } catch (error) {
      return this.handleError(error, 'findDuplicateCustomers');
    }
  }

  /**
   * الحصول على رسالة الإشعار
   */
  getNotificationMessage(action, data) {
    const messages = {
      create: `تم إنشاء عميل جديد: ${data.name}`,
      update: `تم تحديث بيانات العميل: ${data.name}`,
      delete: `تم حذف العميل: ${data.name}`,
      import: `تم استيراد ${data.success} عميل من أصل ${data.total}`
    };
    
    return messages[action] || null;
  }

  /**
   * معالجة البيانات بعد القراءة
   */
  afterRead(customer) {
    // تحميل العلاقات
    customer = this.model.loadRelationships(customer);
    
    // تنسيق التواريخ
    if (customer.created_at) {
      customer.created_at_formatted = new Date(customer.created_at).toLocaleDateString('ar-EG');
    }
    
    if (customer.registration_date) {
      customer.registration_date_formatted = new Date(customer.registration_date).toLocaleDateString('ar-EG');
    }
    
    // تنسيق الحالة
    customer.status_text = customer.status === 'active' ? 'نشط' : 'غير نشط';
    
    return customer;
  }

  /**
   * معالجة البيانات قبل الإنشاء
   */
  beforeCreate(data) {
    // تنظيف البيانات المدخلة
    data = this.sanitizeInput(data);
    
    // التحقق من البيانات المطلوبة
    if (!data.name || !data.email) {
      throw new Error('الاسم والبريد الإلكتروني مطلوبان');
    }
    
    // تعيين الحالة الافتراضية
    if (!data.status) {
      data.status = 'active';
    }
    
    return data;
  }

  /**
   * معالجة البيانات قبل التحديث
   */
  beforeUpdate(id, data) {
    // تنظيف البيانات المدخلة
    data = this.sanitizeInput(data);
    
    // منع تغيير بعض الحقول الحساسة
    delete data.customer_code;
    delete data.registration_date;
    
    return data;
  }

  /**
   * التحقق من إمكانية الحذف
   */
  beforeDelete(id, record) {
    // استخدام التحقق من النموذج
    return this.model.canDelete(id, record);
  }
}
