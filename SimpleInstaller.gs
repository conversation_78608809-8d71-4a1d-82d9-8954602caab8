/**
 * مثبت مبسط للنظام
 * SimpleInstaller.gs
 * نسخة مبسطة من المثبت لتجنب مشاكل الذاكرة
 */

/**
 * تثبيت مبسط للنظام
 */
function simpleInstallSystem(adminData) {
  try {
    Logger.log('بدء التثبيت المبسط...');
    
    // التحقق من عدم وجود تثبيت سابق
    if (isSystemInstalled()) {
      return { success: false, error: 'النظام مثبت بالفعل' };
    }

    // الخطوة 1: إنشاء قاعدة البيانات
    Logger.log('إنشاء قاعدة البيانات...');
    const db = createSimpleDatabase();
    if (!db) {
      return { success: false, error: 'فشل في إنشاء قاعدة البيانات' };
    }

    // الخطوة 2: إنشاء المستخدم الرئيسي
    Logger.log('إنشاء المستخدم الرئيسي...');
    const userResult = createSimpleAdminUser(adminData);
    if (!userResult.success) {
      return userResult;
    }

    // الخطوة 3: تعيين النظام كمثبت
    markSystemAsInstalled();
    
    Logger.log('تم التثبيت المبسط بنجاح');
    
    return {
      success: true,
      message: 'تم تثبيت النظام بنجاح'
    };
    
  } catch (error) {
    Logger.log('خطأ في التثبيت المبسط: ' + error.toString());
    return {
      success: false,
      error: 'خطأ في التثبيت: ' + error.toString()
    };
  }
}

/**
 * إنشاء قاعدة بيانات مبسطة
 */
function createSimpleDatabase() {
  try {
    // البحث عن جدول البيانات الموجود
    const files = DriveApp.getFilesByName(CONFIG.DATABASE.SPREADSHEET_NAME);
    
    let spreadsheet;
    if (files.hasNext()) {
      const file = files.next();
      spreadsheet = SpreadsheetApp.openById(file.getId());
    } else {
      // إنشاء جدول بيانات جديد
      spreadsheet = SpreadsheetApp.create(CONFIG.DATABASE.SPREADSHEET_NAME);
    }

    // حذف الورقة الافتراضية إن وجدت
    try {
      const defaultSheet = spreadsheet.getSheetByName('Sheet1');
      if (defaultSheet) {
        spreadsheet.deleteSheet(defaultSheet);
      }
    } catch (error) {
      // تجاهل الخطأ
    }

    // إنشاء الجداول الأساسية فقط
    createEssentialTables(spreadsheet);
    
    return spreadsheet;
  } catch (error) {
    Logger.log('خطأ في createSimpleDatabase: ' + error.toString());
    return null;
  }
}

/**
 * إنشاء الجداول الأساسية فقط
 */
function createEssentialTables(spreadsheet) {
  try {
    // جدول المستخدمين
    createUsersTableSimple(spreadsheet);
    
    // جدول الصلاحيات
    createPermissionsTableSimple(spreadsheet);
    
    // جدول العملاء (CRM)
    createCRMTableSimple(spreadsheet);
    
    Logger.log('تم إنشاء الجداول الأساسية');
  } catch (error) {
    Logger.log('خطأ في createEssentialTables: ' + error.toString());
    throw error;
  }
}

/**
 * إنشاء جدول المستخدمين المبسط
 */
function createUsersTableSimple(spreadsheet) {
  try {
    const sheet = spreadsheet.insertSheet('users');
    const headers = ['id', 'email', 'name', 'password_hash', 'role', 'status', 'created_at'];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.setFrozenRows(1);
    
    Logger.log('تم إنشاء جدول المستخدمين');
  } catch (error) {
    Logger.log('خطأ في createUsersTableSimple: ' + error.toString());
    throw error;
  }
}

/**
 * إنشاء جدول الصلاحيات المبسط
 */
function createPermissionsTableSimple(spreadsheet) {
  try {
    const sheet = spreadsheet.insertSheet('permissions');
    const headers = ['id', 'user_email', 'module', 'create', 'read', 'update', 'delete', 'created_at'];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.setFrozenRows(1);
    
    Logger.log('تم إنشاء جدول الصلاحيات');
  } catch (error) {
    Logger.log('خطأ في createPermissionsTableSimple: ' + error.toString());
    throw error;
  }
}

/**
 * إنشاء جدول CRM المبسط
 */
function createCRMTableSimple(spreadsheet) {
  try {
    const sheet = spreadsheet.insertSheet('crm');
    const headers = ['id', 'name', 'email', 'phone', 'company', 'status', 'created_at'];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.setFrozenRows(1);
    
    Logger.log('تم إنشاء جدول CRM');
  } catch (error) {
    Logger.log('خطأ في createCRMTableSimple: ' + error.toString());
    throw error;
  }
}

/**
 * إنشاء المستخدم الرئيسي المبسط
 */
function createSimpleAdminUser(adminData) {
  try {
    if (!adminData || !adminData.email || !adminData.password) {
      return { success: false, error: 'بيانات المدير غير مكتملة' };
    }

    // الحصول على جدول البيانات
    const files = DriveApp.getFilesByName(CONFIG.DATABASE.SPREADSHEET_NAME);
    if (!files.hasNext()) {
      return { success: false, error: 'قاعدة البيانات غير موجودة' };
    }
    
    const spreadsheet = SpreadsheetApp.openById(files.next().getId());
    const usersSheet = spreadsheet.getSheetByName('users');
    
    if (!usersSheet) {
      return { success: false, error: 'جدول المستخدمين غير موجود' };
    }

    // إنشاء المستخدم
    const userId = 'ADMIN_' + Date.now();
    const hashedPassword = hashPasswordSimple(adminData.password);
    
    const userData = [
      userId,
      adminData.email,
      adminData.name || 'مدير النظام',
      hashedPassword,
      'ADMIN',
      'active',
      new Date()
    ];
    
    usersSheet.appendRow(userData);
    
    // إنشاء الصلاحيات
    createAdminPermissionsSimple(spreadsheet, adminData.email);
    
    Logger.log('تم إنشاء المستخدم الرئيسي: ' + adminData.email);
    
    return { success: true, userId: userId };
  } catch (error) {
    Logger.log('خطأ في createSimpleAdminUser: ' + error.toString());
    return { success: false, error: error.toString() };
  }
}

/**
 * إنشاء صلاحيات المدير المبسطة
 */
function createAdminPermissionsSimple(spreadsheet, adminEmail) {
  try {
    const permissionsSheet = spreadsheet.getSheetByName('permissions');
    if (!permissionsSheet) {
      throw new Error('جدول الصلاحيات غير موجود');
    }

    // إنشاء صلاحيات للموديولات الأساسية
    const modules = ['CRM', 'Sales', 'Products', 'Inventory'];
    
    modules.forEach(module => {
      const permissionData = [
        'PERM_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5),
        adminEmail,
        module,
        true, // create
        true, // read
        true, // update
        true, // delete
        new Date()
      ];
      
      permissionsSheet.appendRow(permissionData);
    });
    
    Logger.log('تم إنشاء صلاحيات المدير');
  } catch (error) {
    Logger.log('خطأ في createAdminPermissionsSimple: ' + error.toString());
    throw error;
  }
}

/**
 * تشفير كلمة المرور المبسط
 */
function hashPasswordSimple(password) {
  try {
    return Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_256, password)
      .map(byte => (byte + 256).toString(16).slice(-2))
      .join('');
  } catch (error) {
    Logger.log('خطأ في hashPasswordSimple: ' + error.toString());
    return password; // في حالة الخطأ، نعيد كلمة المرور كما هي (غير آمن ولكن للاختبار)
  }
}

/**
 * إضافة جداول إضافية لاحقاً
 */
function addRemainingTables() {
  try {
    Logger.log('إضافة الجداول المتبقية...');
    
    const files = DriveApp.getFilesByName(CONFIG.DATABASE.SPREADSHEET_NAME);
    if (!files.hasNext()) {
      return { success: false, error: 'قاعدة البيانات غير موجودة' };
    }
    
    const spreadsheet = SpreadsheetApp.openById(files.next().getId());
    
    // إضافة جداول الموديولات المتبقية
    const remainingModules = ['Sales', 'Products', 'Inventory', 'Purchases'];
    
    remainingModules.forEach(module => {
      try {
        const tableName = module.toLowerCase();
        
        // التحقق من عدم وجود الجدول
        if (!spreadsheet.getSheetByName(tableName)) {
          const sheet = spreadsheet.insertSheet(tableName);
          const headers = getBasicHeaders(module);
          
          sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
          sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
          sheet.setFrozenRows(1);
          
          Logger.log(`تم إنشاء جدول ${tableName}`);
          
          // تنظيف الذاكرة
          Utilities.sleep(500);
        }
      } catch (error) {
        Logger.log(`خطأ في إنشاء جدول ${module}: ${error.toString()}`);
      }
    });
    
    return { success: true };
  } catch (error) {
    Logger.log('خطأ في addRemainingTables: ' + error.toString());
    return { success: false, error: error.toString() };
  }
}

/**
 * الحصول على العناوين الأساسية للموديول
 */
function getBasicHeaders(moduleName) {
  const commonHeaders = ['id', 'created_at', 'updated_at', 'status'];
  
  const moduleHeaders = {
    'Sales': ['invoice_number', 'customer_id', 'total', 'payment_method'],
    'Products': ['name', 'description', 'price', 'sku'],
    'Inventory': ['product_id', 'quantity', 'warehouse'],
    'Purchases': ['po_number', 'supplier_id', 'total']
  };
  
  const specificHeaders = moduleHeaders[moduleName] || ['name', 'description'];
  return [...commonHeaders, ...specificHeaders];
}

/**
 * اختبار التثبيت المبسط
 */
function testSimpleInstallation() {
  try {
    const testAdminData = {
      name: 'مدير تجريبي',
      email: '<EMAIL>',
      password: 'admin123'
    };

    Logger.log('بدء اختبار التثبيت المبسط...');
    
    const result = simpleInstallSystem(testAdminData);
    
    Logger.log('نتيجة اختبار التثبيت المبسط: ' + JSON.stringify(result));
    
    return result;
  } catch (error) {
    Logger.log('خطأ في اختبار التثبيت المبسط: ' + error.toString());
    return { success: false, error: error.toString() };
  }
}
