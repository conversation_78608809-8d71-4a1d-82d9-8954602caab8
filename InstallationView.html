<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام ERP</title>
    <base target="_top">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .installation-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }

        .installation-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .installation-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .installation-header h1 {
            margin: 0;
            font-weight: 600;
        }

        .installation-body {
            padding: 2rem;
        }

        .step {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .step-item {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 0.5rem;
        }

        .step-number.active {
            background: #667eea;
            color: white;
        }

        .step-number.completed {
            background: #28a745;
            color: white;
        }

        .step-line {
            width: 50px;
            height: 2px;
            background: #e9ecef;
            margin: 0 0.5rem;
        }

        .step-line.completed {
            background: #28a745;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
        }

        .feature-list li i {
            color: #28a745;
            margin-left: 0.5rem;
        }

        .progress-container {
            margin: 2rem 0;
        }

        .installation-progress {
            height: 10px;
            border-radius: 5px;
            overflow: hidden;
            background: #e9ecef;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
        }

        .log-entry.success {
            color: #28a745;
        }

        .log-entry.error {
            color: #dc3545;
        }

        .log-entry.info {
            color: #17a2b8;
        }

        .btn-install {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-install:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-install:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }

        .admin-form {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .success-animation {
            text-align: center;
            padding: 2rem;
        }

        .success-icon {
            font-size: 4rem;
            color: #28a745;
            animation: bounce 1s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }
    </style>
</head>
<body>
    <div class="installation-container">
        <div class="installation-card">
            <div class="installation-header">
                <i class="fas fa-cog fa-3x mb-3"></i>
                <h1>تثبيت نظام ERP المتكامل</h1>
                <p>مرحباً بك في معالج تثبيت النظام</p>
            </div>

            <div class="installation-body">
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step-item">
                        <div class="step-number active" id="step1Number">1</div>
                        <span>مرحباً</span>
                    </div>
                    <div class="step-line" id="line1"></div>
                    <div class="step-item">
                        <div class="step-number" id="step2Number">2</div>
                        <span>الإعداد</span>
                    </div>
                    <div class="step-line" id="line2"></div>
                    <div class="step-item">
                        <div class="step-number" id="step3Number">3</div>
                        <span>التثبيت</span>
                    </div>
                    <div class="step-line" id="line3"></div>
                    <div class="step-item">
                        <div class="step-number" id="step4Number">4</div>
                        <span>اكتمل</span>
                    </div>
                </div>

                <!-- Step 1: Welcome -->
                <div class="step active" id="step1">
                    <h3 class="text-center mb-4">مرحباً بك في نظام ERP المتكامل</h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-star text-warning me-2"></i>مميزات النظام</h5>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i>إدارة العملاء (CRM)</li>
                                <li><i class="fas fa-check"></i>نظام المبيعات ونقاط البيع</li>
                                <li><i class="fas fa-check"></i>إدارة المنتجات والمخزون</li>
                                <li><i class="fas fa-check"></i>نظام المشتريات</li>
                                <li><i class="fas fa-check"></i>إدارة التصنيع</li>
                                <li><i class="fas fa-check"></i>النظام المحاسبي</li>
                                <li><i class="fas fa-check"></i>توزيع البضائع مع الخرائط</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-shield-alt text-success me-2"></i>الأمان والموثوقية</h5>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i>نظام صلاحيات متقدم</li>
                                <li><i class="fas fa-check"></i>نسخ احتياطية تلقائية</li>
                                <li><i class="fas fa-check"></i>تشفير البيانات</li>
                                <li><i class="fas fa-check"></i>سجل العمليات</li>
                                <li><i class="fas fa-check"></i>واجهة متجاوبة</li>
                                <li><i class="fas fa-check"></i>دعم اللغة العربية</li>
                            </ul>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button class="btn btn-install" onclick="nextStep()">
                            <i class="fas fa-arrow-left me-2"></i>التالي
                        </button>
                    </div>
                </div>

                <!-- Step 2: Configuration -->
                <div class="step" id="step2">
                    <h3 class="text-center mb-4">إعداد المدير الرئيسي</h3>
                    
                    <div class="admin-form">
                        <form id="adminForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="adminName" class="form-label">اسم المدير</label>
                                        <input type="text" class="form-control" id="adminName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="adminEmail" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="adminEmail" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="adminPassword" class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" id="adminPassword" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                                        <input type="password" class="form-control" id="confirmPassword" required>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="companyName" class="form-label">اسم الشركة/المؤسسة</label>
                                <input type="text" class="form-control" id="companyName" required>
                            </div>
                        </form>
                    </div>

                    <div class="text-center">
                        <button class="btn btn-secondary me-2" onclick="prevStep()">
                            <i class="fas fa-arrow-right me-2"></i>السابق
                        </button>
                        <button class="btn btn-install" onclick="validateAndNext()">
                            <i class="fas fa-arrow-left me-2"></i>التالي
                        </button>
                    </div>
                </div>

                <!-- Step 3: Installation -->
                <div class="step" id="step3">
                    <h3 class="text-center mb-4">جاري تثبيت النظام</h3>
                    
                    <div class="progress-container">
                        <div class="installation-progress">
                            <div class="progress-bar" id="progressBar"></div>
                        </div>
                        <div class="text-center mt-2">
                            <span id="progressText">0%</span>
                        </div>
                    </div>

                    <div class="log-container" id="logContainer">
                        <div class="log-entry info">بدء عملية التثبيت...</div>
                    </div>

                    <div class="text-center mt-3">
                        <button class="btn btn-install" id="installButton" onclick="startInstallation()">
                            <i class="fas fa-download me-2"></i>بدء التثبيت
                        </button>
                    </div>
                </div>

                <!-- Step 4: Complete -->
                <div class="step" id="step4">
                    <div class="success-animation">
                        <i class="fas fa-check-circle success-icon"></i>
                        <h3 class="mt-3">تم التثبيت بنجاح!</h3>
                        <p class="text-muted">تم تثبيت نظام ERP بنجاح وهو جاهز للاستخدام</p>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                                <ul class="text-start">
                                    <li>تم إنشاء جميع قواعد البيانات المطلوبة</li>
                                    <li>تم إعداد حساب المدير الرئيسي</li>
                                    <li>تم تطبيق الإعدادات الافتراضية</li>
                                    <li>النظام جاهز للاستخدام الفوري</li>
                                </ul>
                            </div>
                        </div>

                        <button class="btn btn-install btn-lg" onclick="completeInstallation()">
                            <i class="fas fa-sign-in-alt me-2"></i>دخول النظام
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let currentStep = 1;
        let adminData = {};

        // Navigation functions
        function nextStep() {
            if (currentStep < 4) {
                showStep(currentStep + 1);
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                showStep(currentStep - 1);
            }
        }

        function showStep(stepNumber) {
            // Hide current step
            document.getElementById(`step${currentStep}`).classList.remove('active');
            document.getElementById(`step${currentStep}Number`).classList.remove('active');
            
            // Show new step
            currentStep = stepNumber;
            document.getElementById(`step${currentStep}`).classList.add('active');
            document.getElementById(`step${currentStep}Number`).classList.add('active');
            
            // Update completed steps
            for (let i = 1; i < currentStep; i++) {
                document.getElementById(`step${i}Number`).classList.add('completed');
                if (i < currentStep - 1) {
                    document.getElementById(`line${i}`).classList.add('completed');
                }
            }
        }

        // Validate admin form and proceed
        function validateAndNext() {
            const name = document.getElementById('adminName').value.trim();
            const email = document.getElementById('adminEmail').value.trim();
            const password = document.getElementById('adminPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const companyName = document.getElementById('companyName').value.trim();

            // Validation
            if (!name || !email || !password || !confirmPassword || !companyName) {
                Swal.fire({
                    title: 'خطأ',
                    text: 'يرجى ملء جميع الحقول المطلوبة',
                    icon: 'error'
                });
                return;
            }

            if (!isValidEmail(email)) {
                Swal.fire({
                    title: 'خطأ',
                    text: 'يرجى إدخال بريد إلكتروني صحيح',
                    icon: 'error'
                });
                return;
            }

            if (password.length < 6) {
                Swal.fire({
                    title: 'خطأ',
                    text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
                    icon: 'error'
                });
                return;
            }

            if (password !== confirmPassword) {
                Swal.fire({
                    title: 'خطأ',
                    text: 'كلمة المرور وتأكيد كلمة المرور غير متطابقتان',
                    icon: 'error'
                });
                return;
            }

            // Store admin data
            adminData = {
                name: name,
                email: email,
                password: password,
                companyName: companyName
            };

            nextStep();
        }

        // Start installation process
        function startInstallation() {
            const button = document.getElementById('installButton');
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التثبيت...';

            // Simulate installation process
            simulateInstallation();
        }

        // Simulate installation process
        function simulateInstallation() {
            const steps = [
                { message: 'إنشاء قاعدة البيانات الرئيسية...', progress: 10 },
                { message: 'إنشاء جداول النظام...', progress: 25 },
                { message: 'إنشاء جداول الموديولات...', progress: 40 },
                { message: 'إعداد المستخدم الرئيسي...', progress: 60 },
                { message: 'تطبيق الإعدادات الافتراضية...', progress: 80 },
                { message: 'اختبار النظام...', progress: 95 },
                { message: 'اكتمل التثبيت بنجاح!', progress: 100 }
            ];

            let currentStepIndex = 0;

            function executeStep() {
                if (currentStepIndex < steps.length) {
                    const step = steps[currentStepIndex];
                    addLogEntry(step.message, 'info');
                    updateProgress(step.progress);

                    if (currentStepIndex === steps.length - 1) {
                        // Last step - call actual installation
                        performActualInstallation();
                    } else {
                        currentStepIndex++;
                        setTimeout(executeStep, 1000 + Math.random() * 1000);
                    }
                }
            }

            executeStep();
        }

        // Perform actual installation
        function performActualInstallation() {
            console.log('Starting actual installation with data:', adminData);
            addLogEntry('بدء التثبيت الفعلي...', 'info');

            google.script.run
                .withSuccessHandler(handleInstallationSuccess)
                .withFailureHandler(handleInstallationError)
                .installSystem(adminData);
        }

        // Handle installation success
        function handleInstallationSuccess(result) {
            console.log('Installation result:', result);
            addLogEntry('استلام نتيجة التثبيت...', 'info');

            if (result && result.success) {
                addLogEntry('تم التثبيت بنجاح!', 'success');
                setTimeout(() => {
                    showStep(4);
                }, 1000);
            } else {
                const errorMsg = result ? result.error : 'نتيجة غير صحيحة';
                addLogEntry('خطأ: ' + errorMsg, 'error');
                handleInstallationError(errorMsg);
            }
        }

        // Handle installation error
        function handleInstallationError(error) {
            console.error('Installation error:', error);
            addLogEntry('خطأ في التثبيت: ' + error, 'error');
            
            const button = document.getElementById('installButton');
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-redo me-2"></i>إعادة المحاولة';
            
            Swal.fire({
                title: 'خطأ في التثبيت',
                text: 'حدث خطأ أثناء التثبيت، يرجى المحاولة مرة أخرى',
                icon: 'error'
            });
        }

        // Add log entry
        function addLogEntry(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `<i class="fas fa-${getLogIcon(type)} me-2"></i>${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Get log icon
        function getLogIcon(type) {
            switch (type) {
                case 'success': return 'check';
                case 'error': return 'times';
                case 'info': return 'info';
                default: return 'info';
            }
        }

        // Update progress
        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressText').textContent = percentage + '%';
        }

        // Complete installation
        function completeInstallation() {
            window.location.reload();
        }

        // Validate email
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Initialize with default values for demo
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('adminName').value = 'مدير النظام';
            document.getElementById('adminEmail').value = '<EMAIL>';
            document.getElementById('adminPassword').value = 'admin123';
            document.getElementById('confirmPassword').value = 'admin123';
            document.getElementById('companyName').value = 'شركة تجريبية';
        });
    </script>
</body>
</html>
