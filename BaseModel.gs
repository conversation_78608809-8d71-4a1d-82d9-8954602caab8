/**
 * النموذج الأساسي (Base Model)
 * BaseModel.gs
 * يحتوي على الفئة الأساسية لجميع النماذج في النظام
 */

/**
 * الفئة الأساسية لجميع النماذج
 */
class BaseModel {
  constructor(tableName) {
    this.tableName = tableName;
    this.db = getDatabase();
    this.requiredFields = [];
    this.validationRules = {};
    this.relationships = {};
  }

  /**
   * إنشاء سجل جديد
   */
  create(data) {
    try {
      // التحقق من صحة البيانات
      const validation = this.validate(data);
      if (!validation.isValid) {
        return { success: false, errors: validation.errors };
      }

      // تطبيق قواعد العمل قبل الإنشاء
      const processedData = this.beforeCreate(data);
      
      // إدراج البيانات
      const result = this.db.insert(this.tableName, processedData);
      
      if (result.success) {
        // تطبيق قواعد العمل بعد الإنشاء
        this.afterCreate(result.id, processedData);
        
        // إرجاع البيانات المحدثة
        const createdRecord = this.findById(result.id);
        return { success: true, data: createdRecord };
      }
      
      return result;
    } catch (error) {
      Logger.log('خطأ في BaseModel.create: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * قراءة سجل بالمعرف
   */
  findById(id) {
    try {
      const record = this.db.findById(this.tableName, id);
      
      if (record) {
        // تحميل العلاقات إذا كانت مطلوبة
        return this.loadRelationships(record);
      }
      
      return null;
    } catch (error) {
      Logger.log('خطأ في BaseModel.findById: ' + error.toString());
      return null;
    }
  }

  /**
   * تحديث سجل موجود
   */
  update(id, data) {
    try {
      // التحقق من وجود السجل
      const existingRecord = this.findById(id);
      if (!existingRecord) {
        return { success: false, error: 'السجل غير موجود' };
      }

      // التحقق من صحة البيانات
      const validation = this.validate(data, id);
      if (!validation.isValid) {
        return { success: false, errors: validation.errors };
      }

      // تطبيق قواعد العمل قبل التحديث
      const processedData = this.beforeUpdate(id, data, existingRecord);
      
      // تحديث البيانات
      const result = this.db.update(this.tableName, id, processedData);
      
      if (result.success) {
        // تطبيق قواعد العمل بعد التحديث
        this.afterUpdate(id, processedData, existingRecord);
        
        // إرجاع البيانات المحدثة
        const updatedRecord = this.findById(id);
        return { success: true, data: updatedRecord };
      }
      
      return result;
    } catch (error) {
      Logger.log('خطأ في BaseModel.update: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * حذف سجل
   */
  delete(id) {
    try {
      // التحقق من وجود السجل
      const existingRecord = this.findById(id);
      if (!existingRecord) {
        return { success: false, error: 'السجل غير موجود' };
      }

      // التحقق من إمكانية الحذف
      const canDelete = this.canDelete(id, existingRecord);
      if (!canDelete.allowed) {
        return { success: false, error: canDelete.reason };
      }

      // تطبيق قواعد العمل قبل الحذف
      this.beforeDelete(id, existingRecord);
      
      // حذف البيانات
      const result = this.db.delete(this.tableName, id);
      
      if (result.success) {
        // تطبيق قواعد العمل بعد الحذف
        this.afterDelete(id, existingRecord);
      }
      
      return result;
    } catch (error) {
      Logger.log('خطأ في BaseModel.delete: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * البحث والاستعلام
   */
  find(criteria = {}, options = {}) {
    try {
      const sheet = this.db.spreadsheet.getSheetByName(this.tableName);
      if (!sheet) {
        return { data: [], total: 0 };
      }

      const data = sheet.getDataRange().getValues();
      if (data.length <= 1) {
        return { data: [], total: 0 };
      }

      const headers = data[0];
      let records = [];

      // تحويل البيانات إلى كائنات
      for (let i = 1; i < data.length; i++) {
        const record = {};
        headers.forEach((header, index) => {
          record[header] = data[i][index];
        });
        records.push(record);
      }

      // تطبيق معايير البحث
      if (Object.keys(criteria).length > 0) {
        records = this.applyCriteria(records, criteria);
      }

      // تطبيق الترتيب
      if (options.orderBy) {
        records = this.applyOrdering(records, options.orderBy, options.orderDirection);
      }

      const total = records.length;

      // تطبيق التصفح
      if (options.limit || options.offset) {
        const offset = options.offset || 0;
        const limit = options.limit || records.length;
        records = records.slice(offset, offset + limit);
      }

      // تحميل العلاقات إذا كانت مطلوبة
      if (options.includeRelationships) {
        records = records.map(record => this.loadRelationships(record));
      }

      return { data: records, total: total };
    } catch (error) {
      Logger.log('خطأ في BaseModel.find: ' + error.toString());
      return { data: [], total: 0, error: error.toString() };
    }
  }

  /**
   * تطبيق معايير البحث
   */
  applyCriteria(records, criteria) {
    return records.filter(record => {
      return Object.keys(criteria).every(key => {
        const criteriaValue = criteria[key];
        const recordValue = record[key];

        if (typeof criteriaValue === 'object' && criteriaValue !== null) {
          // معايير متقدمة
          if (criteriaValue.$like) {
            return recordValue && recordValue.toString().toLowerCase()
              .includes(criteriaValue.$like.toLowerCase());
          }
          if (criteriaValue.$gt) {
            return recordValue > criteriaValue.$gt;
          }
          if (criteriaValue.$lt) {
            return recordValue < criteriaValue.$lt;
          }
          if (criteriaValue.$in) {
            return criteriaValue.$in.includes(recordValue);
          }
        } else {
          // مطابقة مباشرة
          return recordValue === criteriaValue;
        }
      });
    });
  }

  /**
   * تطبيق الترتيب
   */
  applyOrdering(records, orderBy, direction = 'asc') {
    return records.sort((a, b) => {
      const aValue = a[orderBy];
      const bValue = b[orderBy];
      
      if (aValue < bValue) return direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return direction === 'asc' ? 1 : -1;
      return 0;
    });
  }

  /**
   * التحقق من صحة البيانات
   */
  validate(data, id = null) {
    const errors = [];

    // التحقق من الحقول المطلوبة
    this.requiredFields.forEach(field => {
      if (!data.hasOwnProperty(field) || data[field] === '' || data[field] === null) {
        errors.push(`الحقل ${field} مطلوب`);
      }
    });

    // تطبيق قواعد التحقق المخصصة
    Object.keys(this.validationRules).forEach(field => {
      if (data.hasOwnProperty(field)) {
        const rule = this.validationRules[field];
        const value = data[field];

        if (rule.type && typeof value !== rule.type) {
          errors.push(`نوع البيانات غير صحيح للحقل ${field}`);
        }

        if (rule.minLength && value.length < rule.minLength) {
          errors.push(`الحقل ${field} يجب أن يكون أطول من ${rule.minLength} أحرف`);
        }

        if (rule.maxLength && value.length > rule.maxLength) {
          errors.push(`الحقل ${field} يجب أن يكون أقصر من ${rule.maxLength} أحرف`);
        }

        if (rule.pattern && !rule.pattern.test(value)) {
          errors.push(`تنسيق الحقل ${field} غير صحيح`);
        }

        if (rule.unique && this.isValueExists(field, value, id)) {
          errors.push(`القيمة ${value} موجودة بالفعل في الحقل ${field}`);
        }
      }
    });

    // التحقق المخصص
    const customValidation = this.customValidation(data, id);
    if (customValidation.length > 0) {
      errors.push(...customValidation);
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * التحقق من وجود قيمة
   */
  isValueExists(field, value, excludeId = null) {
    try {
      const sheet = this.db.spreadsheet.getSheetByName(this.tableName);
      if (!sheet) return false;

      const data = sheet.getDataRange().getValues();
      const headers = data[0];
      const fieldIndex = headers.indexOf(field);
      const idIndex = headers.indexOf('id');

      if (fieldIndex === -1) return false;

      for (let i = 1; i < data.length; i++) {
        const rowValue = data[i][fieldIndex];
        const rowId = data[i][idIndex];

        if (rowValue === value && rowId !== excludeId) {
          return true;
        }
      }

      return false;
    } catch (error) {
      Logger.log('خطأ في isValueExists: ' + error.toString());
      return false;
    }
  }

  /**
   * تحميل العلاقات
   */
  loadRelationships(record) {
    // يتم تنفيذها في النماذج الفرعية حسب الحاجة
    return record;
  }

  /**
   * التحقق من إمكانية الحذف
   */
  canDelete(id, record) {
    // يمكن تخصيصها في النماذج الفرعية
    return { allowed: true };
  }

  // دوال الأحداث (Events) - يمكن تخصيصها في النماذج الفرعية
  beforeCreate(data) { return data; }
  afterCreate(id, data) { }
  beforeUpdate(id, data, existingRecord) { return data; }
  afterUpdate(id, data, existingRecord) { }
  beforeDelete(id, record) { }
  afterDelete(id, record) { }
  customValidation(data, id) { return []; }

  /**
   * الحصول على عدد السجلات
   */
  count(criteria = {}) {
    const result = this.find(criteria);
    return result.total;
  }

  /**
   * البحث عن سجل واحد
   */
  findOne(criteria = {}) {
    const result = this.find(criteria, { limit: 1 });
    return result.data.length > 0 ? result.data[0] : null;
  }

  /**
   * التحقق من وجود سجل
   */
  exists(criteria = {}) {
    return this.count(criteria) > 0;
  }

  /**
   * الحصول على جميع السجلات
   */
  all(options = {}) {
    return this.find({}, options);
  }

  /**
   * حذف متعدد
   */
  deleteMany(criteria = {}) {
    try {
      const records = this.find(criteria).data;
      const results = [];

      records.forEach(record => {
        const result = this.delete(record.id);
        results.push({ id: record.id, success: result.success, error: result.error });
      });

      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      return {
        success: failCount === 0,
        total: results.length,
        deleted: successCount,
        failed: failCount,
        results: results
      };
    } catch (error) {
      Logger.log('خطأ في BaseModel.deleteMany: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * تحديث متعدد
   */
  updateMany(criteria = {}, updateData = {}) {
    try {
      const records = this.find(criteria).data;
      const results = [];

      records.forEach(record => {
        const result = this.update(record.id, updateData);
        results.push({ id: record.id, success: result.success, error: result.error });
      });

      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      return {
        success: failCount === 0,
        total: results.length,
        updated: successCount,
        failed: failCount,
        results: results
      };
    } catch (error) {
      Logger.log('خطأ في BaseModel.updateMany: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }
}
