/**
 * نموذج إدارة العملاء (CRM Model)
 * CRMModel.gs
 * يدير جميع العمليات المتعلقة ببيانات العملاء
 */

/**
 * فئة نموذج CRM
 */
class CRMModel extends BaseModel {
  constructor() {
    super('crm');
    
    // تحديد الحقول المطلوبة
    this.requiredFields = ['name', 'email'];
    
    // قواعد التحقق من صحة البيانات
    this.validationRules = {
      name: {
        type: 'string',
        minLength: 2,
        maxLength: 100
      },
      email: {
        type: 'string',
        pattern: CONFIG.VALIDATION.EMAIL_REGEX,
        unique: true
      },
      phone: {
        type: 'string',
        pattern: CONFIG.VALIDATION.PHONE_REGEX
      },
      company: {
        type: 'string',
        maxLength: 100
      },
      address: {
        type: 'string',
        maxLength: 255
      },
      notes: {
        type: 'string',
        maxLength: 1000
      }
    };

    // تحديد العلاقات
    this.relationships = {
      sales: { model: 'Sales', foreignKey: 'customer_id' },
      visits: { model: 'VanDistribution', foreignKey: 'customer_id' }
    };
  }

  /**
   * البحث عن العملاء بمعايير متقدمة
   */
  searchCustomers(searchTerm, filters = {}) {
    try {
      const criteria = {};
      
      // البحث العام في الاسم والبريد الإلكتروني والشركة
      if (searchTerm) {
        // في التنفيذ الحقيقي، يمكن البحث في عدة حقول
        criteria.name = { $like: searchTerm };
      }
      
      // تطبيق المرشحات
      if (filters.status) {
        criteria.status = filters.status;
      }
      
      if (filters.company) {
        criteria.company = { $like: filters.company };
      }
      
      if (filters.city) {
        criteria.address = { $like: filters.city };
      }
      
      // البحث بالتاريخ
      if (filters.dateFrom || filters.dateTo) {
        criteria.created_at = {};
        if (filters.dateFrom) {
          criteria.created_at.$gt = new Date(filters.dateFrom);
        }
        if (filters.dateTo) {
          criteria.created_at.$lt = new Date(filters.dateTo);
        }
      }
      
      return this.find(criteria, {
        orderBy: filters.orderBy || 'name',
        orderDirection: filters.orderDirection || 'asc',
        limit: filters.limit,
        offset: filters.offset
      });
      
    } catch (error) {
      Logger.log('خطأ في searchCustomers: ' + error.toString());
      return { data: [], total: 0, error: error.toString() };
    }
  }

  /**
   * الحصول على العملاء النشطين
   */
  getActiveCustomers() {
    return this.find({ status: 'active' }, {
      orderBy: 'name',
      orderDirection: 'asc'
    });
  }

  /**
   * الحصول على العملاء الجدد (آخر 30 يوم)
   */
  getNewCustomers(days = 30) {
    const dateFrom = new Date();
    dateFrom.setDate(dateFrom.getDate() - days);
    
    return this.find({
      created_at: { $gt: dateFrom }
    }, {
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
  }

  /**
   * الحصول على أفضل العملاء (بناءً على المبيعات)
   */
  getTopCustomers(limit = 10) {
    try {
      // في التنفيذ الحقيقي، نحتاج لربط مع جدول المبيعات
      // هنا سنعيد العملاء النشطين كمثال
      return this.find({ status: 'active' }, {
        orderBy: 'created_at',
        orderDirection: 'desc',
        limit: limit
      });
    } catch (error) {
      Logger.log('خطأ في getTopCustomers: ' + error.toString());
      return { data: [], total: 0, error: error.toString() };
    }
  }

  /**
   * الحصول على إحصائيات العملاء
   */
  getCustomerStats() {
    try {
      const stats = {
        total: this.count(),
        active: this.count({ status: 'active' }),
        inactive: this.count({ status: 'inactive' }),
        newThisMonth: 0,
        newThisWeek: 0
      };

      // العملاء الجدد هذا الشهر
      const thisMonth = new Date();
      thisMonth.setDate(1);
      thisMonth.setHours(0, 0, 0, 0);
      stats.newThisMonth = this.count({
        created_at: { $gt: thisMonth }
      });

      // العملاء الجدد هذا الأسبوع
      const thisWeek = new Date();
      thisWeek.setDate(thisWeek.getDate() - thisWeek.getDay());
      thisWeek.setHours(0, 0, 0, 0);
      stats.newThisWeek = this.count({
        created_at: { $gt: thisWeek }
      });

      return { success: true, data: stats };
    } catch (error) {
      Logger.log('خطأ في getCustomerStats: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * تحميل العلاقات للعميل
   */
  loadRelationships(customer) {
    try {
      // تحميل المبيعات المرتبطة (مثال)
      // في التنفيذ الحقيقي، نحتاج للربط مع جدول المبيعات
      customer.salesCount = 0;
      customer.totalSales = 0;
      customer.lastSaleDate = null;

      // تحميل الزيارات المرتبطة
      customer.visitsCount = 0;
      customer.lastVisitDate = null;

      return customer;
    } catch (error) {
      Logger.log('خطأ في loadRelationships: ' + error.toString());
      return customer;
    }
  }

  /**
   * التحقق من إمكانية حذف العميل
   */
  canDelete(id, customer) {
    try {
      // التحقق من وجود مبيعات مرتبطة
      // في التنفيذ الحقيقي، نتحقق من جدول المبيعات
      const hasSales = false; // this.hasSales(id);
      
      if (hasSales) {
        return {
          allowed: false,
          reason: 'لا يمكن حذف العميل لوجود مبيعات مرتبطة به'
        };
      }

      // التحقق من وجود زيارات مرتبطة
      const hasVisits = false; // this.hasVisits(id);
      
      if (hasVisits) {
        return {
          allowed: false,
          reason: 'لا يمكن حذف العميل لوجود زيارات مرتبطة به'
        };
      }

      return { allowed: true };
    } catch (error) {
      Logger.log('خطأ في canDelete: ' + error.toString());
      return {
        allowed: false,
        reason: 'خطأ في التحقق من إمكانية الحذف'
      };
    }
  }

  /**
   * التحقق المخصص من صحة البيانات
   */
  customValidation(data, id) {
    const errors = [];

    // التحقق من تنسيق البريد الإلكتروني
    if (data.email && !CONFIG.VALIDATION.EMAIL_REGEX.test(data.email)) {
      errors.push('تنسيق البريد الإلكتروني غير صحيح');
    }

    // التحقق من تنسيق رقم الهاتف
    if (data.phone && !CONFIG.VALIDATION.PHONE_REGEX.test(data.phone)) {
      errors.push('تنسيق رقم الهاتف غير صحيح');
    }

    // التحقق من طول الاسم
    if (data.name && data.name.length < 2) {
      errors.push('اسم العميل يجب أن يكون أطول من حرفين');
    }

    return errors;
  }

  /**
   * معالجة البيانات قبل الإنشاء
   */
  beforeCreate(data) {
    // تنظيف البيانات
    if (data.name) {
      data.name = data.name.trim();
    }
    
    if (data.email) {
      data.email = data.email.toLowerCase().trim();
    }
    
    if (data.phone) {
      data.phone = data.phone.replace(/\s+/g, '');
    }

    // إضافة بيانات افتراضية
    data.customer_code = this.generateCustomerCode();
    data.registration_date = new Date();
    
    return data;
  }

  /**
   * معالجة البيانات قبل التحديث
   */
  beforeUpdate(id, data, existingRecord) {
    // تنظيف البيانات
    if (data.name) {
      data.name = data.name.trim();
    }
    
    if (data.email) {
      data.email = data.email.toLowerCase().trim();
    }
    
    if (data.phone) {
      data.phone = data.phone.replace(/\s+/g, '');
    }

    // منع تغيير كود العميل
    delete data.customer_code;
    delete data.registration_date;
    
    return data;
  }

  /**
   * معالجة ما بعد الإنشاء
   */
  afterCreate(id, data) {
    try {
      // تسجيل إنشاء عميل جديد
      Logger.log(`تم إنشاء عميل جديد: ${data.name} (${id})`);
      
      // يمكن إضافة إشعارات أو عمليات أخرى هنا
    } catch (error) {
      Logger.log('خطأ في afterCreate: ' + error.toString());
    }
  }

  /**
   * معالجة ما بعد التحديث
   */
  afterUpdate(id, data, existingRecord) {
    try {
      // تسجيل تحديث العميل
      Logger.log(`تم تحديث العميل: ${existingRecord.name} (${id})`);
      
      // يمكن إضافة إشعارات أو عمليات أخرى هنا
    } catch (error) {
      Logger.log('خطأ في afterUpdate: ' + error.toString());
    }
  }

  /**
   * معالجة ما بعد الحذف
   */
  afterDelete(id, record) {
    try {
      // تسجيل حذف العميل
      Logger.log(`تم حذف العميل: ${record.name} (${id})`);
      
      // يمكن إضافة عمليات تنظيف أخرى هنا
    } catch (error) {
      Logger.log('خطأ في afterDelete: ' + error.toString());
    }
  }

  /**
   * توليد كود العميل
   */
  generateCustomerCode() {
    try {
      const prefix = 'CUS';
      const timestamp = Date.now().toString().slice(-6);
      const random = Math.random().toString(36).substr(2, 3).toUpperCase();
      
      return `${prefix}${timestamp}${random}`;
    } catch (error) {
      Logger.log('خطأ في generateCustomerCode: ' + error.toString());
      return 'CUS' + Date.now();
    }
  }

  /**
   * تصدير العملاء إلى CSV
   */
  exportToCSV(criteria = {}) {
    try {
      const customers = this.find(criteria).data;
      
      if (!customers || customers.length === 0) {
        return { success: false, error: 'لا توجد بيانات للتصدير' };
      }

      // تحديد الأعمدة للتصدير
      const headers = [
        'كود العميل', 'الاسم', 'البريد الإلكتروني', 'الهاتف',
        'الشركة', 'العنوان', 'الحالة', 'تاريخ التسجيل'
      ];

      let csvContent = headers.join(',') + '\n';

      customers.forEach(customer => {
        const row = [
          customer.customer_code || '',
          customer.name || '',
          customer.email || '',
          customer.phone || '',
          customer.company || '',
          customer.address || '',
          customer.status || '',
          customer.registration_date ? new Date(customer.registration_date).toLocaleDateString('ar-EG') : ''
        ];
        
        csvContent += row.map(field => `"${field}"`).join(',') + '\n';
      });

      return {
        success: true,
        data: csvContent,
        filename: `customers_${new Date().toISOString().split('T')[0]}.csv`
      };
    } catch (error) {
      Logger.log('خطأ في exportToCSV: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * استيراد العملاء من CSV
   */
  importFromCSV(csvData) {
    try {
      const lines = csvData.split('\n');
      const headers = lines[0].split(',');
      const results = {
        total: 0,
        success: 0,
        failed: 0,
        errors: []
      };

      for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim() === '') continue;
        
        results.total++;
        
        try {
          const values = lines[i].split(',');
          const customerData = {};
          
          headers.forEach((header, index) => {
            const cleanHeader = header.trim().replace(/"/g, '');
            const cleanValue = values[index] ? values[index].trim().replace(/"/g, '') : '';
            
            // تحويل العناوين العربية إلى أسماء الحقول
            switch (cleanHeader) {
              case 'الاسم':
                customerData.name = cleanValue;
                break;
              case 'البريد الإلكتروني':
                customerData.email = cleanValue;
                break;
              case 'الهاتف':
                customerData.phone = cleanValue;
                break;
              case 'الشركة':
                customerData.company = cleanValue;
                break;
              case 'العنوان':
                customerData.address = cleanValue;
                break;
              case 'الحالة':
                customerData.status = cleanValue || 'active';
                break;
            }
          });

          const result = this.create(customerData);
          
          if (result.success) {
            results.success++;
          } else {
            results.failed++;
            results.errors.push(`السطر ${i + 1}: ${result.errors ? result.errors.join(', ') : result.error}`);
          }
          
        } catch (error) {
          results.failed++;
          results.errors.push(`السطر ${i + 1}: ${error.toString()}`);
        }
      }

      return {
        success: true,
        data: results
      };
    } catch (error) {
      Logger.log('خطأ في importFromCSV: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }
}
