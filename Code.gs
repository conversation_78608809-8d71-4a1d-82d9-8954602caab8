/**
 * نظام ERP احترافي ومتكامل
 * الملف الرئيسي - Code.gs
 * يحتوي على الدوال الأساسية لتشغيل النظام
 */

/**
 * دالة تشغيل النظام الرئيسية
 * تقوم بإنشاء واجهة المستخدم وتحميل الصفحة الرئيسية
 */
function doGet(e) {
  try {
    // التحقق من التثبيت الأولي
    if (!isSystemInstalled()) {
      return createInstallationPage();
    }
    
    // التحقق من المصادقة
    const user = getCurrentUser();
    if (!user) {
      return createLoginPage();
    }
    
    // تحميل الصفحة الرئيسية
    return createMainPage(user);
    
  } catch (error) {
    Logger.log('خطأ في doGet: ' + error.toString());
    return createErrorPage(error.toString());
  }
}

/**
 * دالة تحميل ملف HTML مع تضمين الموارد
 */
function include(filename) {
  try {
    return HtmlService.createHtmlOutputFromFile(filename).getContent();
  } catch (error) {
    Logger.log('خطأ في تحميل الملف: ' + filename + ' - ' + error.toString());
    return '<!-- خطأ في تحميل الملف: ' + filename + ' -->';
  }
}

/**
 * إنشاء صفحة التثبيت الأولي
 */
function createInstallationPage() {
  const html = HtmlService.createHtmlOutputFromFile('InstallationView')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
    .setTitle('تثبيت نظام ERP');
  return html;
}

/**
 * إنشاء صفحة تسجيل الدخول
 */
function createLoginPage() {
  const html = HtmlService.createHtmlOutputFromFile('LoginView')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
    .setTitle('تسجيل الدخول - نظام ERP');
  return html;
}

/**
 * إنشاء الصفحة الرئيسية
 */
function createMainPage(user) {
  const html = HtmlService.createHtmlOutputFromFile('MainView')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
    .setTitle('نظام ERP - الصفحة الرئيسية');
  return html;
}

/**
 * إنشاء صفحة الخطأ
 */
function createErrorPage(errorMessage) {
  const html = HtmlService.createTemplate(`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>خطأ - نظام ERP</title>
      <base target="_top">
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
      <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
      <div class="container mt-5">
        <div class="row justify-content-center">
          <div class="col-md-6">
            <div class="card border-danger">
              <div class="card-header bg-danger text-white text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4>حدث خطأ في النظام</h4>
              </div>
              <div class="card-body">
                <p class="text-danger"><?= errorMessage ?></p>
                <div class="text-center">
                  <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-refresh me-2"></i>إعادة المحاولة
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `);
  html.errorMessage = errorMessage;
  return html.evaluate()
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
    .setTitle('خطأ - نظام ERP');
}

/**
 * دالة تحميل صفحة معينة
 */
function loadPage(pageName, params = {}) {
  try {
    const viewName = pageName + 'View';
    const html = HtmlService.createHtmlOutputFromFile(viewName);
    
    // تمرير المعاملات إلى الصفحة
    if (Object.keys(params).length > 0) {
      const template = HtmlService.createTemplateFromFile(viewName);
      Object.keys(params).forEach(key => {
        template[key] = params[key];
      });
      return template.evaluate().getContent();
    }
    
    return html.getContent();
  } catch (error) {
    Logger.log('خطأ في تحميل الصفحة: ' + pageName + ' - ' + error.toString());
    return '<div class="alert alert-danger">خطأ في تحميل الصفحة: ' + pageName + '</div>';
  }
}

/**
 * دالة معالجة طلبات AJAX
 */
function handleAjaxRequest(action, module, data = {}) {
  try {
    // التحقق من المصادقة
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'غير مصرح بالوصول' };
    }
    
    // التحقق من الصلاحيات
    if (!hasPermission(user, module, action)) {
      return { success: false, error: 'ليس لديك صلاحية لتنفيذ هذا الإجراء' };
    }
    
    // تنفيذ الإجراء المطلوب
    const controllerName = module + 'Controller';
    const controller = eval('new ' + controllerName + '()');
    
    switch (action) {
      case 'create':
        return controller.create(data);
      case 'read':
        return controller.read(data.id);
      case 'update':
        return controller.update(data.id, data);
      case 'delete':
        return controller.delete(data.id);
      case 'list':
        return controller.list(data);
      default:
        if (typeof controller[action] === 'function') {
          return controller[action](data);
        }
        return { success: false, error: 'إجراء غير معروف: ' + action };
    }
    
  } catch (error) {
    Logger.log('خطأ في handleAjaxRequest: ' + error.toString());
    return { success: false, error: error.toString() };
  }
}

/**
 * دالة الحصول على بيانات المستخدم الحالي
 */
function getCurrentUserData() {
  try {
    const user = getCurrentUser();
    if (!user) {
      return null;
    }
    
    return {
      email: user.email,
      name: user.name,
      permissions: getUserPermissions(user.email),
      lastLogin: user.lastLogin
    };
  } catch (error) {
    Logger.log('خطأ في getCurrentUserData: ' + error.toString());
    return null;
  }
}

/**
 * دالة الحصول على قائمة الموديولات المتاحة للمستخدم
 */
function getAvailableModules() {
  try {
    const user = getCurrentUser();
    if (!user) {
      return [];
    }
    
    const allModules = CONFIG.MODULES;
    const userPermissions = getUserPermissions(user.email);
    
    return allModules.filter(module => {
      return userPermissions.some(permission => 
        permission.module === module.name && permission.read === true
      );
    });
    
  } catch (error) {
    Logger.log('خطأ في getAvailableModules: ' + error.toString());
    return [];
  }
}

/**
 * دالة تسجيل الخروج
 */
function logout() {
  try {
    // مسح بيانات الجلسة
    PropertiesService.getUserProperties().deleteProperty('current_user');
    return { success: true };
  } catch (error) {
    Logger.log('خطأ في logout: ' + error.toString());
    return { success: false, error: error.toString() };
  }
}

/**
 * دالة اختبار النظام
 */
function testSystem() {
  try {
    const results = {
      database: testDatabaseConnection(),
      modules: testModules(),
      permissions: testPermissions(),
      installation: isSystemInstalled()
    };
    
    Logger.log('نتائج اختبار النظام: ' + JSON.stringify(results));
    return results;
  } catch (error) {
    Logger.log('خطأ في testSystem: ' + error.toString());
    return { error: error.toString() };
  }
}

/**
 * دالة اختبار الموديولات
 */
function testModules() {
  const results = {};

  CONFIG.MODULES.forEach(module => {
    try {
      const modelName = module.name + 'Model';
      const controllerName = module.name + 'Controller';

      // اختبار وجود الموديل والكونترولر
      results[module.name] = {
        model: typeof eval(modelName) === 'function',
        controller: typeof eval(controllerName) === 'function',
        database: testModuleDatabase(module.name)
      };
    } catch (error) {
      results[module.name] = { error: error.toString() };
    }
  });

  return results;
}

/**
 * دالة تثبيت النظام (واجهة للـ InstallationController)
 */
function installSystem(adminData) {
  try {
    Logger.log('بدء تثبيت النظام مع البيانات: ' + JSON.stringify(adminData));

    // التحقق من البيانات المطلوبة
    if (!adminData || !adminData.email || !adminData.password) {
      throw new Error('بيانات المدير غير مكتملة');
    }

    // إنشاء مثيل من متحكم التثبيت إذا لم يكن موجوداً
    if (typeof installationController === 'undefined') {
      const controller = new InstallationController();
      const result = controller.installSystem(adminData);
      Logger.log('نتيجة التثبيت: ' + JSON.stringify(result));
      return result;
    }

    const result = installationController.installSystem(adminData);
    Logger.log('نتيجة التثبيت: ' + JSON.stringify(result));
    return result;
  } catch (error) {
    Logger.log('خطأ في installSystem: ' + error.toString());
    return { success: false, error: error.toString() };
  }
}

/**
 * دالة اختبار قاعدة بيانات موديول معين
 */
function testModuleDatabase(moduleName) {
  try {
    const db = getDatabase();
    const tableName = moduleName.toLowerCase();
    const sheet = db.spreadsheet.getSheetByName(tableName);

    return {
      exists: !!sheet,
      rowCount: sheet ? sheet.getLastRow() - 1 : 0,
      columnCount: sheet ? sheet.getLastColumn() : 0
    };

  } catch (error) {
    Logger.log('خطأ في testModuleDatabase: ' + error.toString());
    return { exists: false, error: error.toString() };
  }
}
