/**
 * مدير الذاكرة
 * MemoryManager.gs
 * يساعد في إدارة استخدام الذاكرة وتجنب نفادها
 */

/**
 * فئة إدارة الذاكرة
 */
class MemoryManager {
  constructor() {
    this.maxExecutionTime = 5 * 60 * 1000; // 5 دقائق
    this.startTime = new Date().getTime();
    this.memoryCheckInterval = 30000; // 30 ثانية
    this.lastMemoryCheck = this.startTime;
  }

  /**
   * التحقق من الوقت المتبقي للتنفيذ
   */
  getRemainingTime() {
    const currentTime = new Date().getTime();
    const elapsedTime = currentTime - this.startTime;
    return this.maxExecutionTime - elapsedTime;
  }

  /**
   * التحقق من إمكانية الاستمرار
   */
  canContinue() {
    return this.getRemainingTime() > 60000; // دقيقة واحدة على الأقل
  }

  /**
   * تنظيف الذاكرة
   */
  cleanMemory() {
    try {
      // إجبار جمع القمامة
      Utilities.sleep(100);
      
      // تحديث وقت آخر تنظيف
      this.lastMemoryCheck = new Date().getTime();
      
      Logger.log('تم تنظيف الذاكرة');
    } catch (error) {
      Logger.log('خطأ في تنظيف الذاكرة: ' + error.toString());
    }
  }

  /**
   * تنظيف دوري للذاكرة
   */
  periodicCleanup() {
    const currentTime = new Date().getTime();
    if (currentTime - this.lastMemoryCheck > this.memoryCheckInterval) {
      this.cleanMemory();
    }
  }

  /**
   * تنفيذ عملية مع إدارة الذاكرة
   */
  executeWithMemoryManagement(operation, context = '') {
    try {
      // التحقق من الوقت المتبقي
      if (!this.canContinue()) {
        throw new Error('انتهى الوقت المسموح للتنفيذ');
      }

      // تنظيف دوري
      this.periodicCleanup();

      // تنفيذ العملية
      const result = operation();

      // تنظيف بعد العملية
      this.cleanMemory();

      return result;
    } catch (error) {
      Logger.log(`خطأ في executeWithMemoryManagement (${context}): ${error.toString()}`);
      throw error;
    }
  }

  /**
   * تنفيذ عمليات متعددة بشكل متسلسل
   */
  executeBatch(operations, batchSize = 5) {
    const results = [];
    
    for (let i = 0; i < operations.length; i += batchSize) {
      // التحقق من الوقت المتبقي
      if (!this.canContinue()) {
        Logger.log('توقف التنفيذ بسبب انتهاء الوقت المسموح');
        break;
      }

      // تنفيذ مجموعة من العمليات
      const batch = operations.slice(i, i + batchSize);
      
      batch.forEach((operation, index) => {
        try {
          const result = this.executeWithMemoryManagement(operation, `batch_${i + index}`);
          results.push({ success: true, result: result });
        } catch (error) {
          results.push({ success: false, error: error.toString() });
        }
      });

      // تنظيف بين المجموعات
      if (i + batchSize < operations.length) {
        Utilities.sleep(1000);
        this.cleanMemory();
      }
    }

    return results;
  }

  /**
   * إنشاء جداول بشكل متدرج
   */
  createTablesGradually(spreadsheet, tableConfigs) {
    const results = [];
    
    tableConfigs.forEach((config, index) => {
      try {
        // التحقق من الوقت المتبقي
        if (!this.canContinue()) {
          Logger.log('توقف إنشاء الجداول بسبب انتهاء الوقت');
          return results;
        }

        Logger.log(`إنشاء جدول ${config.name}...`);
        
        // إنشاء الجدول
        const sheet = spreadsheet.insertSheet(config.name);
        
        // إضافة العناوين
        if (config.headers && config.headers.length > 0) {
          sheet.getRange(1, 1, 1, config.headers.length).setValues([config.headers]);
          sheet.getRange(1, 1, 1, config.headers.length).setFontWeight('bold');
          sheet.setFrozenRows(1);
        }

        // تطبيق التنسيق
        if (config.format) {
          this.applyTableFormatting(sheet, config.headers);
        }

        results.push({ success: true, tableName: config.name });
        Logger.log(`تم إنشاء جدول ${config.name} بنجاح`);

        // تنظيف الذاكرة بين كل جدول
        if (index < tableConfigs.length - 1) {
          Utilities.sleep(500);
          this.cleanMemory();
        }

      } catch (error) {
        Logger.log(`خطأ في إنشاء جدول ${config.name}: ${error.toString()}`);
        results.push({ success: false, tableName: config.name, error: error.toString() });
      }
    });

    return results;
  }

  /**
   * تطبيق تنسيق الجدول
   */
  applyTableFormatting(sheet, headers) {
    try {
      if (!headers || headers.length === 0) return;

      // تنسيق الرأس
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setBackground('#4285f4');
      headerRange.setFontColor('white');
      headerRange.setFontWeight('bold');
      
      // تعيين عرض الأعمدة
      headers.forEach((header, index) => {
        let width = 100;
        if (header.includes('id')) width = 80;
        else if (header.includes('email')) width = 150;
        else if (header.includes('name')) width = 120;
        else if (header.includes('description') || header.includes('notes')) width = 200;
        else if (header.includes('date')) width = 120;
        
        sheet.setColumnWidth(index + 1, width);
      });
      
    } catch (error) {
      Logger.log('خطأ في تطبيق تنسيق الجدول: ' + error.toString());
    }
  }

  /**
   * معالجة البيانات بشكل متدرج
   */
  processDataInChunks(data, processor, chunkSize = 100) {
    const results = [];
    
    for (let i = 0; i < data.length; i += chunkSize) {
      // التحقق من الوقت المتبقي
      if (!this.canContinue()) {
        Logger.log('توقف معالجة البيانات بسبب انتهاء الوقت');
        break;
      }

      const chunk = data.slice(i, i + chunkSize);
      
      try {
        const chunkResults = this.executeWithMemoryManagement(() => {
          return processor(chunk);
        }, `chunk_${i}`);
        
        results.push(...chunkResults);
        
        // تنظيف بين المجموعات
        if (i + chunkSize < data.length) {
          Utilities.sleep(200);
        }
        
      } catch (error) {
        Logger.log(`خطأ في معالجة المجموعة ${i}: ${error.toString()}`);
      }
    }

    return results;
  }

  /**
   * إحصائيات الاستخدام
   */
  getUsageStats() {
    const currentTime = new Date().getTime();
    const elapsedTime = currentTime - this.startTime;
    
    return {
      elapsedTime: elapsedTime,
      remainingTime: this.getRemainingTime(),
      canContinue: this.canContinue(),
      memoryCleanups: Math.floor(elapsedTime / this.memoryCheckInterval)
    };
  }
}

// إنشاء مثيل عام من مدير الذاكرة
const memoryManager = new MemoryManager();

/**
 * دوال مساعدة عامة
 */

/**
 * تنظيف الذاكرة
 */
function cleanMemory() {
  memoryManager.cleanMemory();
}

/**
 * التحقق من إمكانية الاستمرار
 */
function canContinueExecution() {
  return memoryManager.canContinue();
}

/**
 * تنفيذ عملية مع إدارة الذاكرة
 */
function executeWithMemoryManagement(operation, context = '') {
  return memoryManager.executeWithMemoryManagement(operation, context);
}

/**
 * إنشاء جداول بشكل آمن
 */
function createTablesGradually(spreadsheet, tableConfigs) {
  return memoryManager.createTablesGradually(spreadsheet, tableConfigs);
}

/**
 * معالجة البيانات بشكل متدرج
 */
function processDataInChunks(data, processor, chunkSize = 100) {
  return memoryManager.processDataInChunks(data, processor, chunkSize);
}

/**
 * الحصول على إحصائيات الاستخدام
 */
function getMemoryUsageStats() {
  return memoryManager.getUsageStats();
}
