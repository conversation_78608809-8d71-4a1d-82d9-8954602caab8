/**
 * إدارة قاعدة البيانات باستخدام Google Sheets
 * Database.gs
 * يحتوي على جميع العمليات المتعلقة بقاعدة البيانات
 */

/**
 * فئة إدارة قاعدة البيانات
 */
class DatabaseManager {
  constructor() {
    this.spreadsheetId = this.getOrCreateMainSpreadsheet();
    this.spreadsheet = SpreadsheetApp.openById(this.spreadsheetId);
  }

  /**
   * الحصول على أو إنشاء جدول البيانات الرئيسي
   */
  getOrCreateMainSpreadsheet() {
    try {
      // البحث عن جدول البيانات الموجود
      const files = DriveApp.getFilesByName(CONFIG.DATABASE.SPREADSHEET_NAME);
      
      if (files.hasNext()) {
        const file = files.next();
        return file.getId();
      }
      
      // إنشاء جدول بيانات جديد
      const spreadsheet = SpreadsheetApp.create(CONFIG.DATABASE.SPREADSHEET_NAME);
      const spreadsheetId = spreadsheet.getId();
      
      // إنشاء الجداول الأساسية
      this.createSystemTables(spreadsheet);
      
      Logger.log('تم إنشاء جدول البيانات الرئيسي: ' + spreadsheetId);
      return spreadsheetId;
      
    } catch (error) {
      Logger.log('خطأ في getOrCreateMainSpreadsheet: ' + error.toString());
      throw error;
    }
  }

  /**
   * إنشاء الجداول الأساسية للنظام (محسن للذاكرة)
   */
  createSystemTables(spreadsheet) {
    try {
      // حذف الورقة الافتراضية
      try {
        const defaultSheet = spreadsheet.getSheetByName('Sheet1');
        if (defaultSheet) {
          spreadsheet.deleteSheet(defaultSheet);
        }
      } catch (error) {
        Logger.log('لا توجد ورقة افتراضية للحذف');
      }

      // إنشاء الجداول الأساسية فقط
      this.createUsersTable(spreadsheet);
      Utilities.sleep(500); // تنظيف الذاكرة

      this.createPermissionsTable(spreadsheet);
      Utilities.sleep(500); // تنظيف الذاكرة

      this.createLogsTable(spreadsheet);
      Utilities.sleep(500); // تنظيف الذاكرة

      Logger.log('تم إنشاء الجداول الأساسية للنظام');

    } catch (error) {
      Logger.log('خطأ في createSystemTables: ' + error.toString());
      throw error;
    }
  }

  /**
   * إنشاء جدول المستخدمين
   */
  createUsersTable(spreadsheet) {
    const sheet = spreadsheet.insertSheet('users');
    const headers = [
      'id', 'email', 'name', 'password_hash', 'role', 
      'status', 'last_login', 'created_at', 'updated_at'
    ];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.setFrozenRows(1);
    
    // إضافة مستخدم افتراضي (admin)
    const adminData = [
      this.generateId(),
      '<EMAIL>',
      'مدير النظام',
      this.hashPassword('admin123'),
      'ADMIN',
      'active',
      '',
      new Date(),
      new Date()
    ];
    
    sheet.getRange(2, 1, 1, adminData.length).setValues([adminData]);
  }

  /**
   * إنشاء جدول الصلاحيات
   */
  createPermissionsTable(spreadsheet) {
    const sheet = spreadsheet.insertSheet('permissions');
    const headers = [
      'id', 'user_email', 'module', 'create', 'read', 
      'update', 'delete', 'export', 'import', 'created_at'
    ];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.setFrozenRows(1);
  }

  /**
   * إنشاء جدول السجلات
   */
  createLogsTable(spreadsheet) {
    const sheet = spreadsheet.insertSheet('system_logs');
    const headers = [
      'id', 'user_email', 'action', 'module', 'record_id',
      'old_data', 'new_data', 'ip_address', 'user_agent', 'created_at'
    ];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.setFrozenRows(1);
  }

  /**
   * إنشاء جدول لموديول معين (محسن للذاكرة)
   */
  createModuleTable(spreadsheet, moduleName) {
    return executeWithMemoryManagement(() => {
      const tableName = moduleName.toLowerCase();

      // التحقق من وجود الجدول
      if (spreadsheet.getSheetByName(tableName)) {
        Logger.log(`جدول ${tableName} موجود بالفعل`);
        return;
      }

      const sheet = spreadsheet.insertSheet(tableName);
      const headers = this.getModuleHeaders(moduleName);

      // إضافة العناوين
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
      sheet.setFrozenRows(1);

      // تطبيق التنسيق
      this.formatSheet(sheet, headers);

      Logger.log(`تم إنشاء جدول ${tableName} بنجاح`);

    }, `createModuleTable_${moduleName}`);
  }

  /**
   * الحصول على عناوين الأعمدة لموديول معين
   */
  getModuleHeaders(moduleName) {
    const commonHeaders = ['id', 'created_at', 'updated_at', 'created_by', 'status'];
    
    const moduleHeaders = {
      'CRM': ['name', 'email', 'phone', 'address', 'company', 'notes'],
      'Sales': ['invoice_number', 'customer_id', 'items', 'subtotal', 'tax', 'total', 'payment_method'],
      'Products': ['name', 'description', 'category', 'price', 'cost', 'sku', 'barcode', 'unit'],
      'Inventory': ['product_id', 'warehouse', 'quantity', 'min_stock', 'max_stock', 'location'],
      'Purchases': ['po_number', 'supplier_id', 'items', 'subtotal', 'tax', 'total', 'delivery_date'],
      'Manufacturing': ['product_id', 'quantity', 'start_date', 'end_date', 'materials', 'labor_cost'],
      'Treasury': ['transaction_type', 'amount', 'description', 'reference', 'balance'],
      'Wallets': ['wallet_name', 'wallet_type', 'balance', 'currency', 'account_number'],
      'Accounting': ['account_code', 'account_name', 'account_type', 'parent_account', 'balance'],
      'VanDistribution': ['van_id', 'driver_id', 'route', 'customers', 'products', 'start_location', 'end_location'],
      'Settings': ['setting_key', 'setting_value', 'setting_type', 'description']
    };
    
    const specificHeaders = moduleHeaders[moduleName] || ['name', 'description'];
    return [...commonHeaders, ...specificHeaders];
  }

  /**
   * تنسيق الورقة
   */
  formatSheet(sheet, headers) {
    try {
      // تنسيق الرأس
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setBackground('#4285f4');
      headerRange.setFontColor('white');
      headerRange.setFontWeight('bold');
      
      // تعيين عرض الأعمدة
      headers.forEach((header, index) => {
        let width = 100;
        if (header.includes('id')) width = 80;
        else if (header.includes('email')) width = 150;
        else if (header.includes('name')) width = 120;
        else if (header.includes('description') || header.includes('notes')) width = 200;
        else if (header.includes('date')) width = 120;
        
        sheet.setColumnWidth(index + 1, width);
      });
      
    } catch (error) {
      Logger.log('خطأ في formatSheet: ' + error.toString());
    }
  }

  /**
   * إدراج سجل جديد
   */
  insert(tableName, data) {
    try {
      const sheet = this.spreadsheet.getSheetByName(tableName);
      if (!sheet) {
        throw new Error('الجدول غير موجود: ' + tableName);
      }
      
      // إضافة البيانات الأساسية
      data.id = data.id || this.generateId();
      data.created_at = new Date();
      data.updated_at = new Date();
      data.created_by = getCurrentUser()?.email || 'system';
      data.status = data.status || 'active';
      
      // الحصول على الرأس
      const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
      
      // ترتيب البيانات حسب الرأس
      const rowData = headers.map(header => data[header] || '');
      
      // إدراج السطر
      sheet.appendRow(rowData);
      
      // تسجيل العملية
      this.logAction('insert', tableName, data.id, null, data);
      
      return { success: true, id: data.id };
      
    } catch (error) {
      Logger.log('خطأ في insert: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * تحديث سجل موجود
   */
  update(tableName, id, data) {
    try {
      const sheet = this.spreadsheet.getSheetByName(tableName);
      if (!sheet) {
        throw new Error('الجدول غير موجود: ' + tableName);
      }
      
      // البحث عن السجل
      const rowIndex = this.findRowById(sheet, id);
      if (rowIndex === -1) {
        throw new Error('السجل غير موجود');
      }
      
      // الحصول على البيانات القديمة
      const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
      const oldData = this.getRowData(sheet, rowIndex, headers);
      
      // تحديث البيانات
      data.updated_at = new Date();
      
      // تحديث الخلايا المطلوبة فقط
      headers.forEach((header, index) => {
        if (data.hasOwnProperty(header)) {
          sheet.getRange(rowIndex, index + 1).setValue(data[header]);
        }
      });
      
      // تسجيل العملية
      this.logAction('update', tableName, id, oldData, data);
      
      return { success: true };
      
    } catch (error) {
      Logger.log('خطأ في update: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * حذف سجل
   */
  delete(tableName, id) {
    try {
      const sheet = this.spreadsheet.getSheetByName(tableName);
      if (!sheet) {
        throw new Error('الجدول غير موجود: ' + tableName);
      }
      
      // البحث عن السجل
      const rowIndex = this.findRowById(sheet, id);
      if (rowIndex === -1) {
        throw new Error('السجل غير موجود');
      }
      
      // الحصول على البيانات قبل الحذف
      const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
      const oldData = this.getRowData(sheet, rowIndex, headers);
      
      // حذف السطر
      sheet.deleteRow(rowIndex);
      
      // تسجيل العملية
      this.logAction('delete', tableName, id, oldData, null);
      
      return { success: true };
      
    } catch (error) {
      Logger.log('خطأ في delete: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * البحث عن سجل بالمعرف
   */
  findById(tableName, id) {
    try {
      const sheet = this.spreadsheet.getSheetByName(tableName);
      if (!sheet) {
        return null;
      }
      
      const rowIndex = this.findRowById(sheet, id);
      if (rowIndex === -1) {
        return null;
      }
      
      const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
      return this.getRowData(sheet, rowIndex, headers);
      
    } catch (error) {
      Logger.log('خطأ في findById: ' + error.toString());
      return null;
    }
  }

  /**
   * البحث عن سطر بالمعرف
   */
  findRowById(sheet, id) {
    const data = sheet.getDataRange().getValues();
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === id) {
        return i + 1; // إرجاع رقم السطر (1-based)
      }
    }
    return -1;
  }

  /**
   * الحصول على بيانات سطر
   */
  getRowData(sheet, rowIndex, headers) {
    const rowData = sheet.getRange(rowIndex, 1, 1, headers.length).getValues()[0];
    const result = {};
    
    headers.forEach((header, index) => {
      result[header] = rowData[index];
    });
    
    return result;
  }

  /**
   * توليد معرف فريد
   */
  generateId() {
    return 'ID_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * تشفير كلمة المرور
   */
  hashPassword(password) {
    return Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_256, password)
      .map(byte => (byte + 256).toString(16).slice(-2))
      .join('');
  }

  /**
   * تسجيل العمليات
   */
  logAction(action, module, recordId, oldData, newData) {
    try {
      const user = getCurrentUser();
      const logData = {
        user_email: user?.email || 'system',
        action: action,
        module: module,
        record_id: recordId,
        old_data: oldData ? JSON.stringify(oldData) : '',
        new_data: newData ? JSON.stringify(newData) : '',
        ip_address: '',
        user_agent: '',
        created_at: new Date()
      };
      
      this.insert('system_logs', logData);
      
    } catch (error) {
      Logger.log('خطأ في logAction: ' + error.toString());
    }
  }
}

/**
 * إنشاء مثيل من مدير قاعدة البيانات
 */
function getDatabase() {
  return new DatabaseManager();
}

/**
 * اختبار الاتصال بقاعدة البيانات
 */
function testDatabaseConnection() {
  try {
    const db = getDatabase();
    return { success: true, spreadsheetId: db.spreadsheetId };
  } catch (error) {
    Logger.log('خطأ في testDatabaseConnection: ' + error.toString());
    return { success: false, error: error.toString() };
  }
}

/**
 * اختبار قاعدة بيانات موديول معين
 */
function testModuleDatabase(moduleName) {
  try {
    const db = getDatabase();
    const tableName = moduleName.toLowerCase();
    const sheet = db.spreadsheet.getSheetByName(tableName);
    
    return {
      exists: !!sheet,
      rowCount: sheet ? sheet.getLastRow() - 1 : 0,
      columnCount: sheet ? sheet.getLastColumn() : 0
    };
    
  } catch (error) {
    Logger.log('خطأ في testModuleDatabase: ' + error.toString());
    return { exists: false, error: error.toString() };
  }
}
