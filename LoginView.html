<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام ERP</title>
    <base target="_top">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            margin: 20px;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-header h2 {
            margin: 0;
            font-weight: 600;
        }

        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }

        .login-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }

        .login-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            color: #6c757d;
        }

        .system-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            text-align: center;
        }

        .system-info small {
            color: #6c757d;
        }

        .loading-spinner {
            display: none;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 0.75rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            display: none;
        }

        .password-toggle {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            z-index: 10;
        }

        .input-group {
            position: relative;
        }

        /* Animation */
        .login-container {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 576px) {
            .login-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .login-header,
            .login-body,
            .login-footer {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Header -->
        <div class="login-header">
            <i class="fas fa-chart-line fa-3x mb-3"></i>
            <h2>نظام ERP المتكامل</h2>
            <p>تسجيل الدخول إلى النظام</p>
        </div>

        <!-- Body -->
        <div class="login-body">
            <!-- Error Message -->
            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span id="errorText"></span>
            </div>

            <!-- Login Form -->
            <form id="loginForm" onsubmit="handleLogin(event)">
                <div class="form-floating">
                    <input type="email" class="form-control" id="email" placeholder="البريد الإلكتروني" required>
                    <label for="email"><i class="fas fa-envelope me-2"></i>البريد الإلكتروني</label>
                </div>

                <div class="form-floating">
                    <div class="input-group">
                        <input type="password" class="form-control" id="password" placeholder="كلمة المرور" required>
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                    <label for="password"><i class="fas fa-lock me-2"></i>كلمة المرور</label>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">
                        تذكرني
                    </label>
                </div>

                <button type="submit" class="btn btn-primary btn-login" id="loginButton">
                    <span class="login-text">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </span>
                    <span class="loading-spinner">
                        <i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...
                    </span>
                </button>
            </form>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <div class="system-info">
                <small>
                    <i class="fas fa-shield-alt me-1"></i>
                    نظام آمن ومحمي
                </small>
                <br>
                <small class="text-muted">الإصدار 1.0.0</small>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Focus on email field
            document.getElementById('email').focus();
            
            // Load remembered email if exists
            const rememberedEmail = localStorage.getItem('rememberedEmail');
            if (rememberedEmail) {
                document.getElementById('email').value = rememberedEmail;
                document.getElementById('rememberMe').checked = true;
                document.getElementById('password').focus();
            }
        });

        // Handle login form submission
        function handleLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            // Validate inputs
            if (!email || !password) {
                showError('يرجى إدخال البريد الإلكتروني وكلمة المرور');
                return;
            }
            
            if (!isValidEmail(email)) {
                showError('يرجى إدخال بريد إلكتروني صحيح');
                return;
            }
            
            // Show loading state
            setLoadingState(true);
            hideError();
            
            // Remember email if requested
            if (rememberMe) {
                localStorage.setItem('rememberedEmail', email);
            } else {
                localStorage.removeItem('rememberedEmail');
            }
            
            // Attempt login
            google.script.run
                .withSuccessHandler(handleLoginSuccess)
                .withFailureHandler(handleLoginError)
                .login(email, password);
        }

        // Handle successful login
        function handleLoginSuccess(result) {
            setLoadingState(false);
            
            if (result.success) {
                // Show success message
                Swal.fire({
                    title: 'تم تسجيل الدخول بنجاح',
                    text: 'مرحباً بك في نظام ERP',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    // Redirect to main page
                    window.location.reload();
                });
            } else {
                showError(result.error || 'فشل في تسجيل الدخول');
            }
        }

        // Handle login error
        function handleLoginError(error) {
            setLoadingState(false);
            console.error('Login error:', error);
            showError('حدث خطأ في الاتصال، يرجى المحاولة مرة أخرى');
        }

        // Toggle password visibility
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Set loading state
        function setLoadingState(loading) {
            const button = document.getElementById('loginButton');
            const loginText = button.querySelector('.login-text');
            const loadingSpinner = button.querySelector('.loading-spinner');
            
            if (loading) {
                button.disabled = true;
                loginText.style.display = 'none';
                loadingSpinner.style.display = 'inline';
            } else {
                button.disabled = false;
                loginText.style.display = 'inline';
                loadingSpinner.style.display = 'none';
            }
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            errorText.textContent = message;
            errorDiv.style.display = 'block';
            
            // Auto hide after 5 seconds
            setTimeout(hideError, 5000);
        }

        // Hide error message
        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        // Validate email format
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Handle Enter key in form fields
        document.getElementById('email').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('password').focus();
            }
        });

        document.getElementById('password').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });

        // Add some demo credentials info (remove in production)
        setTimeout(() => {
            if (document.getElementById('email').value === '') {
                Swal.fire({
                    title: 'بيانات تجريبية',
                    html: `
                        <p>يمكنك استخدام البيانات التالية للدخول:</p>
                        <strong>البريد الإلكتروني:</strong> <EMAIL><br>
                        <strong>كلمة المرور:</strong> admin123
                    `,
                    icon: 'info',
                    confirmButtonText: 'موافق'
                });
            }
        }, 2000);
    </script>
</body>
</html>
