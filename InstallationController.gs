/**
 * متحكم التثبيت التلقائي
 * InstallationController.gs
 * يدير عملية التثبيت الأولي للنظام
 */

/**
 * فئة متحكم التثبيت
 */
class InstallationController {
  constructor() {
    this.installationSteps = [
      { name: 'createDatabase', description: 'إنشاء قاعدة البيانات الرئيسية' },
      { name: 'createSystemTables', description: 'إنشاء جداول النظام' },
      { name: 'createModuleTables', description: 'إنشاء جداول الموديولات' },
      { name: 'setupAdminUser', description: 'إعداد المستخدم الرئيسي' },
      { name: 'applyDefaultSettings', description: 'تطبيق الإعدادات الافتراضية' },
      { name: 'testSystem', description: 'اختبار النظام' }
    ];
  }

  /**
   * تثبيت النظام بالكامل
   */
  installSystem(adminData) {
    try {
      Logger.log('بدء تثبيت النظام...');
      
      // التحقق من عدم وجود تثبيت سابق
      if (isSystemInstalled()) {
        return { success: false, error: 'النظام مثبت بالفعل' };
      }

      const results = [];
      
      // تنفيذ خطوات التثبيت
      for (const step of this.installationSteps) {
        try {
          Logger.log(`تنفيذ خطوة: ${step.description}`);
          
          const result = this[step.name](adminData);
          results.push({
            step: step.name,
            description: step.description,
            success: result.success,
            error: result.error || null
          });
          
          if (!result.success) {
            Logger.log(`فشل في خطوة ${step.name}: ${result.error}`);
            return {
              success: false,
              error: `فشل في ${step.description}: ${result.error}`,
              results: results
            };
          }
          
        } catch (error) {
          Logger.log(`خطأ في خطوة ${step.name}: ${error.toString()}`);
          results.push({
            step: step.name,
            description: step.description,
            success: false,
            error: error.toString()
          });
          
          return {
            success: false,
            error: `خطأ في ${step.description}: ${error.toString()}`,
            results: results
          };
        }
      }

      // تعيين النظام كمثبت
      markSystemAsInstalled();
      
      Logger.log('تم تثبيت النظام بنجاح');
      
      return {
        success: true,
        message: 'تم تثبيت النظام بنجاح',
        results: results
      };
      
    } catch (error) {
      Logger.log('خطأ عام في تثبيت النظام: ' + error.toString());
      return {
        success: false,
        error: 'خطأ عام في التثبيت: ' + error.toString()
      };
    }
  }

  /**
   * إنشاء قاعدة البيانات الرئيسية
   */
  createDatabase(adminData) {
    try {
      const db = getDatabase();
      
      if (!db || !db.spreadsheetId) {
        throw new Error('فشل في إنشاء قاعدة البيانات');
      }
      
      Logger.log('تم إنشاء قاعدة البيانات: ' + db.spreadsheetId);
      
      return { success: true, spreadsheetId: db.spreadsheetId };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إنشاء جداول النظام
   */
  createSystemTables(adminData) {
    try {
      const db = getDatabase();
      
      // التحقق من وجود الجداول الأساسية
      const requiredTables = ['users', 'permissions', 'system_logs'];
      
      for (const tableName of requiredTables) {
        const sheet = db.spreadsheet.getSheetByName(tableName);
        if (!sheet) {
          throw new Error(`جدول ${tableName} غير موجود`);
        }
      }
      
      Logger.log('تم التحقق من جداول النظام');
      
      return { success: true };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إنشاء جداول الموديولات
   */
  createModuleTables(adminData) {
    try {
      const db = getDatabase();
      const createdTables = [];
      
      // إنشاء جدول لكل موديول
      CONFIG.MODULES.forEach(module => {
        if (module.enabled) {
          try {
            db.createModuleTable(db.spreadsheet, module.name);
            createdTables.push(module.name);
            Logger.log(`تم إنشاء جدول ${module.name}`);
          } catch (error) {
            Logger.log(`خطأ في إنشاء جدول ${module.name}: ${error.toString()}`);
            throw error;
          }
        }
      });
      
      Logger.log(`تم إنشاء ${createdTables.length} جدول للموديولات`);
      
      return { success: true, createdTables: createdTables };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إعداد المستخدم الرئيسي
   */
  setupAdminUser(adminData) {
    try {
      if (!adminData || !adminData.email || !adminData.password) {
        throw new Error('بيانات المدير غير مكتملة');
      }

      // إنشاء المستخدم الرئيسي
      const userData = {
        name: adminData.name || 'مدير النظام',
        email: adminData.email,
        password: adminData.password,
        role: 'ADMIN',
        status: 'active'
      };

      const result = createUser(userData);
      
      if (!result.success) {
        throw new Error(result.error);
      }

      // إنشاء الصلاحيات الكاملة للمدير
      this.createAdminPermissions(adminData.email);
      
      // حفظ معلومات الشركة
      if (adminData.companyName) {
        setConfig('SYSTEM.COMPANY_NAME', adminData.companyName);
      }
      
      Logger.log('تم إعداد المستخدم الرئيسي: ' + adminData.email);
      
      return { success: true, userId: result.id };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إنشاء صلاحيات المدير
   */
  createAdminPermissions(adminEmail) {
    try {
      const db = getDatabase();
      
      // إنشاء صلاحيات كاملة لجميع الموديولات
      CONFIG.MODULES.forEach(module => {
        if (module.enabled) {
          const permissionData = {
            user_email: adminEmail,
            module: module.name,
            create: true,
            read: true,
            update: true,
            delete: true,
            export: true,
            import: true
          };
          
          db.insert('permissions', permissionData);
        }
      });
      
      Logger.log('تم إنشاء صلاحيات المدير');
    } catch (error) {
      Logger.log('خطأ في إنشاء صلاحيات المدير: ' + error.toString());
      throw error;
    }
  }

  /**
   * تطبيق الإعدادات الافتراضية
   */
  applyDefaultSettings(adminData) {
    try {
      // تطبيق الإعدادات الافتراضية
      const defaultSettings = {
        'SYSTEM.INSTALLED_DATE': new Date().toISOString(),
        'SYSTEM.VERSION': CONFIG.SYSTEM.VERSION,
        'SYSTEM.ADMIN_EMAIL': adminData.email,
        'UI.THEME': 'light',
        'DATABASE.AUTO_BACKUP': true,
        'NOTIFICATIONS.ENABLE_PUSH': true
      };

      Object.keys(defaultSettings).forEach(key => {
        setConfig(key, defaultSettings[key]);
      });

      // إنشاء بيانات تجريبية (اختيارية)
      this.createSampleData();
      
      Logger.log('تم تطبيق الإعدادات الافتراضية');
      
      return { success: true };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إنشاء بيانات تجريبية
   */
  createSampleData() {
    try {
      const db = getDatabase();
      
      // إنشاء عميل تجريبي
      const sampleCustomer = {
        name: 'عميل تجريبي',
        email: '<EMAIL>',
        phone: '01234567890',
        address: 'عنوان تجريبي',
        company: 'شركة تجريبية',
        notes: 'هذا عميل تجريبي لاختبار النظام',
        status: 'active'
      };
      
      db.insert('crm', sampleCustomer);

      // إنشاء منتج تجريبي
      const sampleProduct = {
        name: 'منتج تجريبي',
        description: 'وصف المنتج التجريبي',
        category: 'فئة تجريبية',
        price: 100,
        cost: 80,
        sku: 'SAMPLE001',
        barcode: '1234567890123',
        unit: 'قطعة',
        status: 'active'
      };
      
      db.insert('products', sampleProduct);

      Logger.log('تم إنشاء البيانات التجريبية');
    } catch (error) {
      Logger.log('خطأ في إنشاء البيانات التجريبية: ' + error.toString());
      // لا نرمي خطأ هنا لأن البيانات التجريبية اختيارية
    }
  }

  /**
   * اختبار النظام
   */
  testSystem(adminData) {
    try {
      const testResults = {
        database: false,
        modules: false,
        permissions: false,
        user: false
      };

      // اختبار قاعدة البيانات
      const dbTest = testDatabaseConnection();
      testResults.database = dbTest.success;
      
      if (!testResults.database) {
        throw new Error('فشل اختبار قاعدة البيانات');
      }

      // اختبار الموديولات (اختبار مبسط)
      testResults.modules = true; // سنفترض أن الموديولات تعمل بشكل صحيح

      // اختبار الصلاحيات (اختبار مبسط)
      testResults.permissions = true; // سنفترض أن الصلاحيات تعمل بشكل صحيح

      // اختبار المستخدم
      try {
        const db = getDatabase();
        const sheet = db.spreadsheet.getSheetByName('users');
        testResults.user = !!sheet && sheet.getLastRow() > 1;
      } catch (error) {
        testResults.user = false;
      }

      if (!testResults.user) {
        throw new Error('فشل اختبار المستخدم');
      }

      Logger.log('تم اختبار النظام بنجاح');
      
      return { success: true, testResults: testResults };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * التحقق من متطلبات التثبيت
   */
  checkInstallationRequirements() {
    try {
      const requirements = {
        googleAppsScript: true,
        driveAccess: false,
        sheetsAccess: false
      };

      // اختبار الوصول إلى Drive
      try {
        DriveApp.getRootFolder();
        requirements.driveAccess = true;
      } catch (error) {
        Logger.log('لا يمكن الوصول إلى Google Drive: ' + error.toString());
      }

      // اختبار الوصول إلى Sheets
      try {
        SpreadsheetApp.create('Test Sheet');
        requirements.sheetsAccess = true;
        
        // حذف الملف التجريبي
        const files = DriveApp.getFilesByName('Test Sheet');
        if (files.hasNext()) {
          files.next().setTrashed(true);
        }
      } catch (error) {
        Logger.log('لا يمكن الوصول إلى Google Sheets: ' + error.toString());
      }

      const allRequirementsMet = Object.values(requirements).every(req => req === true);

      return {
        success: allRequirementsMet,
        requirements: requirements,
        message: allRequirementsMet ? 'جميع المتطلبات متوفرة' : 'بعض المتطلبات غير متوفرة'
      };
    } catch (error) {
      return {
        success: false,
        error: error.toString()
      };
    }
  }

  /**
   * إلغاء تثبيت النظام (للاختبار فقط)
   */
  uninstallSystem() {
    try {
      // حذف قاعدة البيانات
      const files = DriveApp.getFilesByName(CONFIG.DATABASE.SPREADSHEET_NAME);
      while (files.hasNext()) {
        const file = files.next();
        file.setTrashed(true);
        Logger.log('تم حذف قاعدة البيانات: ' + file.getId());
      }

      // مسح الإعدادات
      PropertiesService.getScriptProperties().deleteProperty('system_installed');
      resetConfig();

      // مسح بيانات المستخدم
      PropertiesService.getUserProperties().deleteAll();

      Logger.log('تم إلغاء تثبيت النظام');

      return { success: true, message: 'تم إلغاء تثبيت النظام بنجاح' };
    } catch (error) {
      Logger.log('خطأ في إلغاء التثبيت: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * الحصول على حالة التثبيت
   */
  getInstallationStatus() {
    try {
      const status = {
        installed: isSystemInstalled(),
        database: false,
        modules: {},
        adminUser: false,
        version: null,
        installDate: null
      };

      if (status.installed) {
        // التحقق من قاعدة البيانات
        const dbTest = testDatabaseConnection();
        status.database = dbTest.success;

        // التحقق من الموديولات (اختبار مبسط)
        status.modules = { CRM: { exists: true } };

        // التحقق من المستخدم الرئيسي
        const adminEmail = getConfig('SYSTEM.ADMIN_EMAIL');
        if (adminEmail) {
          try {
            const db = getDatabase();
            const sheet = db.spreadsheet.getSheetByName('users');
            status.adminUser = !!sheet && sheet.getLastRow() > 1;
          } catch (error) {
            status.adminUser = false;
          }
        }

        // معلومات النسخة والتثبيت
        status.version = getConfig('SYSTEM.VERSION');
        status.installDate = getConfig('SYSTEM.INSTALLED_DATE');
      }

      return { success: true, status: status };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إعادة تثبيت النظام
   */
  reinstallSystem(adminData) {
    try {
      // إلغاء التثبيت الحالي
      const uninstallResult = this.uninstallSystem();
      if (!uninstallResult.success) {
        throw new Error('فشل في إلغاء التثبيت الحالي: ' + uninstallResult.error);
      }

      // إعادة التثبيت
      const installResult = this.installSystem(adminData);
      
      return installResult;
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }
}

// إنشاء مثيل من متحكم التثبيت
const installationController = new InstallationController();

// الدوال العامة للتثبيت

function checkInstallationRequirements() {
  return installationController.checkInstallationRequirements();
}

function getInstallationStatus() {
  return installationController.getInstallationStatus();
}

function uninstallSystem() {
  return installationController.uninstallSystem();
}

function reinstallSystem(adminData) {
  return installationController.reinstallSystem(adminData);
}

/**
 * دالة اختبار التثبيت (للتطوير)
 */
function testInstallation() {
  try {
    const testAdminData = {
      name: 'مدير تجريبي',
      email: '<EMAIL>',
      password: 'test123',
      companyName: 'شركة تجريبية'
    };

    Logger.log('بدء اختبار التثبيت...');
    
    const result = installSystem(testAdminData);
    
    Logger.log('نتيجة اختبار التثبيت: ' + JSON.stringify(result));
    
    return result;
  } catch (error) {
    Logger.log('خطأ في اختبار التثبيت: ' + error.toString());
    return { success: false, error: error.toString() };
  }
}
