/**
 * متحكم التثبيت التلقائي
 * InstallationController.gs
 * يدير عملية التثبيت الأولي للنظام
 */

/**
 * فئة متحكم التثبيت
 */
class InstallationController {
  constructor() {
    this.installationSteps = [
      { name: 'createDatabase', description: 'إنشاء قاعدة البيانات الرئيسية' },
      { name: 'createSystemTables', description: 'إنشاء جداول النظام' },
      { name: 'createModuleTables', description: 'إنشاء جداول الموديولات' },
      { name: 'setupAdminUser', description: 'إعداد المستخدم الرئيسي' },
      { name: 'applyDefaultSettings', description: 'تطبيق الإعدادات الافتراضية' },
      { name: 'testSystem', description: 'اختبار النظام' }
    ];
  }

  /**
   * تثبيت النظام بالكامل (محسن لتجنب نفاد الذاكرة)
   */
  installSystem(adminData) {
    try {
      Logger.log('بدء تثبيت النظام...');

      // التحقق من عدم وجود تثبيت سابق
      if (isSystemInstalled()) {
        return { success: false, error: 'النظام مثبت بالفعل' };
      }

      // تنفيذ التثبيت على مراحل لتجنب نفاد الذاكرة
      return this.installInStages(adminData);

    } catch (error) {
      Logger.log('خطأ عام في تثبيت النظام: ' + error.toString());
      return {
        success: false,
        error: 'خطأ عام في التثبيت: ' + error.toString()
      };
    }
  }

  /**
   * تثبيت النظام على مراحل
   */
  installInStages(adminData) {
    try {
      // المرحلة 1: إنشاء قاعدة البيانات والجداول الأساسية
      const stage1 = this.installStage1(adminData);
      if (!stage1.success) return stage1;

      // تنظيف الذاكرة
      Utilities.sleep(1000);

      // المرحلة 2: إنشاء جداول الموديولات (جزء أول)
      const stage2 = this.installStage2(adminData);
      if (!stage2.success) return stage2;

      // تنظيف الذاكرة
      Utilities.sleep(1000);

      // المرحلة 3: إعداد المستخدم والإعدادات
      const stage3 = this.installStage3(adminData);
      if (!stage3.success) return stage3;

      // تعيين النظام كمثبت
      markSystemAsInstalled();

      Logger.log('تم تثبيت النظام بنجاح');

      return {
        success: true,
        message: 'تم تثبيت النظام بنجاح'
      };

    } catch (error) {
      Logger.log('خطأ في التثبيت على مراحل: ' + error.toString());
      return {
        success: false,
        error: 'خطأ في التثبيت: ' + error.toString()
      };
    }
  }

  /**
   * إنشاء قاعدة البيانات الرئيسية
   */
  createDatabase(adminData) {
    try {
      const db = getDatabase();
      
      if (!db || !db.spreadsheetId) {
        throw new Error('فشل في إنشاء قاعدة البيانات');
      }
      
      Logger.log('تم إنشاء قاعدة البيانات: ' + db.spreadsheetId);
      
      return { success: true, spreadsheetId: db.spreadsheetId };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إنشاء جداول النظام
   */
  createSystemTables(adminData) {
    try {
      const db = getDatabase();
      
      // التحقق من وجود الجداول الأساسية
      const requiredTables = ['users', 'permissions', 'system_logs'];
      
      for (const tableName of requiredTables) {
        const sheet = db.spreadsheet.getSheetByName(tableName);
        if (!sheet) {
          throw new Error(`جدول ${tableName} غير موجود`);
        }
      }
      
      Logger.log('تم التحقق من جداول النظام');
      
      return { success: true };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * المرحلة 1: إنشاء قاعدة البيانات والجداول الأساسية
   */
  installStage1(adminData) {
    try {
      Logger.log('المرحلة 1: إنشاء قاعدة البيانات...');

      // إنشاء قاعدة البيانات
      const dbResult = this.createDatabase(adminData);
      if (!dbResult.success) return dbResult;

      // إنشاء جداول النظام
      const tablesResult = this.createSystemTables(adminData);
      if (!tablesResult.success) return tablesResult;

      Logger.log('تمت المرحلة 1 بنجاح');
      return { success: true };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * المرحلة 2: إنشاء جداول الموديولات
   */
  installStage2(adminData) {
    try {
      Logger.log('المرحلة 2: إنشاء جداول الموديولات...');

      const db = getDatabase();
      const createdTables = [];

      // إنشاء جداول الموديولات الأساسية فقط لتجنب نفاد الذاكرة
      const essentialModules = ['CRM', 'Sales', 'Products', 'Inventory'];

      essentialModules.forEach(moduleName => {
        try {
          db.createModuleTable(db.spreadsheet, moduleName);
          createdTables.push(moduleName);
          Logger.log(`تم إنشاء جدول ${moduleName}`);

          // تنظيف الذاكرة بين كل جدول
          Utilities.sleep(200);
        } catch (error) {
          Logger.log(`خطأ في إنشاء جدول ${moduleName}: ${error.toString()}`);
          throw error;
        }
      });

      Logger.log(`تم إنشاء ${createdTables.length} جدول أساسي`);
      return { success: true, createdTables: createdTables };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * المرحلة 3: إعداد المستخدم والإعدادات
   */
  installStage3(adminData) {
    try {
      Logger.log('المرحلة 3: إعداد المستخدم والإعدادات...');

      // إعداد المستخدم الرئيسي
      const userResult = this.setupAdminUser(adminData);
      if (!userResult.success) return userResult;

      // تطبيق الإعدادات الافتراضية (مبسطة)
      const settingsResult = this.applyBasicSettings(adminData);
      if (!settingsResult.success) return settingsResult;

      Logger.log('تمت المرحلة 3 بنجاح');
      return { success: true };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إعداد المستخدم الرئيسي
   */
  setupAdminUser(adminData) {
    try {
      if (!adminData || !adminData.email || !adminData.password) {
        throw new Error('بيانات المدير غير مكتملة');
      }

      // إنشاء المستخدم الرئيسي
      const userData = {
        name: adminData.name || 'مدير النظام',
        email: adminData.email,
        password: adminData.password,
        role: 'ADMIN',
        status: 'active'
      };

      const result = createUser(userData);
      
      if (!result.success) {
        throw new Error(result.error);
      }

      // إنشاء الصلاحيات الكاملة للمدير
      this.createAdminPermissions(adminData.email);
      
      // حفظ معلومات الشركة
      if (adminData.companyName) {
        setConfig('SYSTEM.COMPANY_NAME', adminData.companyName);
      }
      
      Logger.log('تم إعداد المستخدم الرئيسي: ' + adminData.email);
      
      return { success: true, userId: result.id };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إنشاء صلاحيات المدير
   */
  createAdminPermissions(adminEmail) {
    try {
      const db = getDatabase();
      
      // إنشاء صلاحيات كاملة لجميع الموديولات
      CONFIG.MODULES.forEach(module => {
        if (module.enabled) {
          const permissionData = {
            user_email: adminEmail,
            module: module.name,
            create: true,
            read: true,
            update: true,
            delete: true,
            export: true,
            import: true
          };
          
          db.insert('permissions', permissionData);
        }
      });
      
      Logger.log('تم إنشاء صلاحيات المدير');
    } catch (error) {
      Logger.log('خطأ في إنشاء صلاحيات المدير: ' + error.toString());
      throw error;
    }
  }

  /**
   * تطبيق الإعدادات الأساسية (مبسطة لتجنب نفاد الذاكرة)
   */
  applyBasicSettings(adminData) {
    try {
      // تطبيق الإعدادات الأساسية فقط
      const basicSettings = {
        'SYSTEM.INSTALLED_DATE': new Date().toISOString(),
        'SYSTEM.VERSION': CONFIG.SYSTEM.VERSION,
        'SYSTEM.ADMIN_EMAIL': adminData.email
      };

      Object.keys(basicSettings).forEach(key => {
        try {
          setConfig(key, basicSettings[key]);
        } catch (error) {
          Logger.log(`خطأ في تعيين الإعداد ${key}: ${error.toString()}`);
        }
      });

      Logger.log('تم تطبيق الإعدادات الأساسية');

      return { success: true };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إنشاء باقي جداول الموديولات (يتم استدعاؤها لاحقاً)
   */
  createRemainingTables() {
    try {
      const db = getDatabase();
      const remainingModules = CONFIG.MODULES.filter(module =>
        module.enabled && !['CRM', 'Sales', 'Products', 'Inventory'].includes(module.name)
      );

      const createdTables = [];

      remainingModules.forEach(module => {
        try {
          db.createModuleTable(db.spreadsheet, module.name);
          createdTables.push(module.name);
          Logger.log(`تم إنشاء جدول ${module.name}`);

          // تنظيف الذاكرة
          Utilities.sleep(300);
        } catch (error) {
          Logger.log(`خطأ في إنشاء جدول ${module.name}: ${error.toString()}`);
        }
      });

      return { success: true, createdTables: createdTables };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إنشاء بيانات تجريبية مبسطة
   */
  createMinimalSampleData() {
    try {
      const db = getDatabase();

      // إنشاء عميل تجريبي واحد فقط
      const sampleCustomer = {
        name: 'عميل تجريبي',
        email: '<EMAIL>',
        phone: '01234567890',
        status: 'active'
      };

      db.insert('crm', sampleCustomer);
      Logger.log('تم إنشاء عميل تجريبي');

    } catch (error) {
      Logger.log('خطأ في إنشاء البيانات التجريبية: ' + error.toString());
      // لا نرمي خطأ هنا لأن البيانات التجريبية اختيارية
    }
  }

  /**
   * اختبار النظام
   */
  testSystem(adminData) {
    try {
      const testResults = {
        database: false,
        modules: false,
        permissions: false,
        user: false
      };

      // اختبار قاعدة البيانات
      const dbTest = testDatabaseConnection();
      testResults.database = dbTest.success;
      
      if (!testResults.database) {
        throw new Error('فشل اختبار قاعدة البيانات');
      }

      // اختبار الموديولات
      const modulesTest = testModules();
      testResults.modules = Object.values(modulesTest).every(result => 
        !result.error && result.model && result.controller
      );
      
      if (!testResults.modules) {
        throw new Error('فشل اختبار الموديولات');
      }

      // اختبار الصلاحيات
      const permissionsTest = testPermissions();
      testResults.permissions = !permissionsTest.error;
      
      if (!testResults.permissions) {
        throw new Error('فشل اختبار الصلاحيات');
      }

      // اختبار المستخدم
      const user = authManager.findUserByEmail(adminData.email);
      testResults.user = !!user;
      
      if (!testResults.user) {
        throw new Error('فشل اختبار المستخدم');
      }

      Logger.log('تم اختبار النظام بنجاح');
      
      return { success: true, testResults: testResults };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * التحقق من متطلبات التثبيت
   */
  checkInstallationRequirements() {
    try {
      const requirements = {
        googleAppsScript: true,
        driveAccess: false,
        sheetsAccess: false
      };

      // اختبار الوصول إلى Drive
      try {
        DriveApp.getRootFolder();
        requirements.driveAccess = true;
      } catch (error) {
        Logger.log('لا يمكن الوصول إلى Google Drive: ' + error.toString());
      }

      // اختبار الوصول إلى Sheets
      try {
        SpreadsheetApp.create('Test Sheet');
        requirements.sheetsAccess = true;
        
        // حذف الملف التجريبي
        const files = DriveApp.getFilesByName('Test Sheet');
        if (files.hasNext()) {
          files.next().setTrashed(true);
        }
      } catch (error) {
        Logger.log('لا يمكن الوصول إلى Google Sheets: ' + error.toString());
      }

      const allRequirementsMet = Object.values(requirements).every(req => req === true);

      return {
        success: allRequirementsMet,
        requirements: requirements,
        message: allRequirementsMet ? 'جميع المتطلبات متوفرة' : 'بعض المتطلبات غير متوفرة'
      };
    } catch (error) {
      return {
        success: false,
        error: error.toString()
      };
    }
  }

  /**
   * إلغاء تثبيت النظام (للاختبار فقط)
   */
  uninstallSystem() {
    try {
      // حذف قاعدة البيانات
      const files = DriveApp.getFilesByName(CONFIG.DATABASE.SPREADSHEET_NAME);
      while (files.hasNext()) {
        const file = files.next();
        file.setTrashed(true);
        Logger.log('تم حذف قاعدة البيانات: ' + file.getId());
      }

      // مسح الإعدادات
      PropertiesService.getScriptProperties().deleteProperty('system_installed');
      resetConfig();

      // مسح بيانات المستخدم
      PropertiesService.getUserProperties().deleteAll();

      Logger.log('تم إلغاء تثبيت النظام');

      return { success: true, message: 'تم إلغاء تثبيت النظام بنجاح' };
    } catch (error) {
      Logger.log('خطأ في إلغاء التثبيت: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * الحصول على حالة التثبيت
   */
  getInstallationStatus() {
    try {
      const status = {
        installed: isSystemInstalled(),
        database: false,
        modules: {},
        adminUser: false,
        version: null,
        installDate: null
      };

      if (status.installed) {
        // التحقق من قاعدة البيانات
        const dbTest = testDatabaseConnection();
        status.database = dbTest.success;

        // التحقق من الموديولات
        status.modules = testModules();

        // التحقق من المستخدم الرئيسي
        const adminEmail = getConfig('SYSTEM.ADMIN_EMAIL');
        if (adminEmail) {
          const user = authManager.findUserByEmail(adminEmail);
          status.adminUser = !!user;
        }

        // معلومات النسخة والتثبيت
        status.version = getConfig('SYSTEM.VERSION');
        status.installDate = getConfig('SYSTEM.INSTALLED_DATE');
      }

      return { success: true, status: status };
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إعادة تثبيت النظام
   */
  reinstallSystem(adminData) {
    try {
      // إلغاء التثبيت الحالي
      const uninstallResult = this.uninstallSystem();
      if (!uninstallResult.success) {
        throw new Error('فشل في إلغاء التثبيت الحالي: ' + uninstallResult.error);
      }

      // إعادة التثبيت
      const installResult = this.installSystem(adminData);
      
      return installResult;
    } catch (error) {
      return { success: false, error: error.toString() };
    }
  }
}

// إنشاء مثيل من متحكم التثبيت
const installationController = new InstallationController();

// الدوال العامة للتثبيت
function installSystem(adminData) {
  return installationController.installSystem(adminData);
}

function checkInstallationRequirements() {
  return installationController.checkInstallationRequirements();
}

function getInstallationStatus() {
  return installationController.getInstallationStatus();
}

function uninstallSystem() {
  return installationController.uninstallSystem();
}

function reinstallSystem(adminData) {
  return installationController.reinstallSystem(adminData);
}

/**
 * دالة اختبار التثبيت (للتطوير)
 */
function testInstallation() {
  try {
    const testAdminData = {
      name: 'مدير تجريبي',
      email: '<EMAIL>',
      password: 'test123',
      companyName: 'شركة تجريبية'
    };

    Logger.log('بدء اختبار التثبيت...');
    
    const result = installSystem(testAdminData);
    
    Logger.log('نتيجة اختبار التثبيت: ' + JSON.stringify(result));
    
    return result;
  } catch (error) {
    Logger.log('خطأ في اختبار التثبيت: ' + error.toString());
    return { success: false, error: error.toString() };
  }
}
