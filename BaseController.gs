/**
 * المتحكم الأساسي (Base Controller)
 * BaseController.gs
 * يحتوي على الفئة الأساسية لجميع المتحكمات في النظام
 */

/**
 * الفئة الأساسية لجميع المتحكمات
 */
class BaseController {
  constructor(modelClass, moduleName) {
    this.model = new modelClass();
    this.moduleName = moduleName;
    this.currentUser = getCurrentUser();
  }

  /**
   * إنشاء سجل جديد
   */
  create(data) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('create')) {
        return { success: false, error: 'ليس لديك صلاحية لإنشاء سجل جديد' };
      }

      // تطبيق قواعد العمل قبل الإنشاء
      const processedData = this.beforeCreate(data);
      
      // إنشاء السجل
      const result = this.model.create(processedData);
      
      if (result.success) {
        // تطبيق قواعد العمل بعد الإنشاء
        this.afterCreate(result.data);
        
        // إرسال الإشعارات
        this.sendNotification('create', result.data);
      }
      
      return result;
    } catch (error) {
      Logger.log('خطأ في BaseController.create: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * قراءة سجل بالمعرف
   */
  read(id) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('read')) {
        return { success: false, error: 'ليس لديك صلاحية لقراءة البيانات' };
      }

      const record = this.model.findById(id);
      
      if (!record) {
        return { success: false, error: 'السجل غير موجود' };
      }

      // تطبيق قواعد العمل بعد القراءة
      const processedRecord = this.afterRead(record);
      
      return { success: true, data: processedRecord };
    } catch (error) {
      Logger.log('خطأ في BaseController.read: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * تحديث سجل موجود
   */
  update(id, data) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('update')) {
        return { success: false, error: 'ليس لديك صلاحية لتحديث البيانات' };
      }

      // تطبيق قواعد العمل قبل التحديث
      const processedData = this.beforeUpdate(id, data);
      
      // تحديث السجل
      const result = this.model.update(id, processedData);
      
      if (result.success) {
        // تطبيق قواعد العمل بعد التحديث
        this.afterUpdate(result.data);
        
        // إرسال الإشعارات
        this.sendNotification('update', result.data);
      }
      
      return result;
    } catch (error) {
      Logger.log('خطأ في BaseController.update: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * حذف سجل
   */
  delete(id) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('delete')) {
        return { success: false, error: 'ليس لديك صلاحية لحذف البيانات' };
      }

      // الحصول على السجل قبل الحذف
      const record = this.model.findById(id);
      if (!record) {
        return { success: false, error: 'السجل غير موجود' };
      }

      // تطبيق قواعد العمل قبل الحذف
      const canDelete = this.beforeDelete(id, record);
      if (!canDelete.allowed) {
        return { success: false, error: canDelete.reason };
      }
      
      // حذف السجل
      const result = this.model.delete(id);
      
      if (result.success) {
        // تطبيق قواعد العمل بعد الحذف
        this.afterDelete(id, record);
        
        // إرسال الإشعارات
        this.sendNotification('delete', record);
      }
      
      return result;
    } catch (error) {
      Logger.log('خطأ في BaseController.delete: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * قائمة السجلات مع البحث والتصفح
   */
  list(params = {}) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('read')) {
        return { success: false, error: 'ليس لديك صلاحية لقراءة البيانات' };
      }

      // إعداد معايير البحث
      const criteria = this.buildSearchCriteria(params);
      
      // إعداد خيارات الاستعلام
      const options = {
        limit: parseInt(params.limit) || CONFIG.UI.ITEMS_PER_PAGE,
        offset: parseInt(params.offset) || 0,
        orderBy: params.orderBy || 'created_at',
        orderDirection: params.orderDirection || 'desc',
        includeRelationships: params.includeRelationships || false
      };

      // تطبيق قواعد العمل قبل البحث
      const processedCriteria = this.beforeList(criteria, options);
      
      // البحث في البيانات
      const result = this.model.find(processedCriteria, options);
      
      if (result.data) {
        // تطبيق قواعد العمل بعد البحث
        result.data = result.data.map(record => this.afterRead(record));
      }
      
      return {
        success: true,
        data: result.data,
        total: result.total,
        page: Math.floor(options.offset / options.limit) + 1,
        totalPages: Math.ceil(result.total / options.limit),
        limit: options.limit
      };
    } catch (error) {
      Logger.log('خطأ في BaseController.list: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * بناء معايير البحث
   */
  buildSearchCriteria(params) {
    const criteria = {};
    
    // البحث العام
    if (params.search) {
      const searchFields = this.getSearchFields();
      if (searchFields.length > 0) {
        // في هذا التنفيذ المبسط، نبحث في الحقل الأول فقط
        criteria[searchFields[0]] = { $like: params.search };
      }
    }

    // المرشحات المحددة
    if (params.filters) {
      Object.keys(params.filters).forEach(key => {
        if (params.filters[key] !== '' && params.filters[key] !== null) {
          criteria[key] = params.filters[key];
        }
      });
    }

    // مرشح الحالة
    if (params.status) {
      criteria.status = params.status;
    }

    // مرشح التاريخ
    if (params.dateFrom || params.dateTo) {
      criteria.created_at = {};
      if (params.dateFrom) {
        criteria.created_at.$gt = new Date(params.dateFrom);
      }
      if (params.dateTo) {
        criteria.created_at.$lt = new Date(params.dateTo);
      }
    }

    return criteria;
  }

  /**
   * الحصول على حقول البحث
   */
  getSearchFields() {
    // يتم تخصيصها في المتحكمات الفرعية
    return ['name'];
  }

  /**
   * التحقق من الصلاحيات
   */
  hasPermission(action) {
    if (!this.currentUser) {
      return false;
    }
    
    return hasPermission(this.currentUser, this.moduleName, action);
  }

  /**
   * تصدير البيانات
   */
  export(params = {}) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('export')) {
        return { success: false, error: 'ليس لديك صلاحية لتصدير البيانات' };
      }

      const format = params.format || 'csv';
      const criteria = this.buildSearchCriteria(params);
      
      // الحصول على جميع البيانات (بدون تصفح)
      const result = this.model.find(criteria, { 
        orderBy: params.orderBy || 'created_at',
        orderDirection: params.orderDirection || 'desc'
      });

      if (!result.data || result.data.length === 0) {
        return { success: false, error: 'لا توجد بيانات للتصدير' };
      }

      // تنسيق البيانات للتصدير
      const exportData = this.formatForExport(result.data);
      
      // إنشاء الملف
      const file = this.createExportFile(exportData, format);
      
      return {
        success: true,
        file: file,
        recordCount: result.data.length
      };
    } catch (error) {
      Logger.log('خطأ في BaseController.export: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * تنسيق البيانات للتصدير
   */
  formatForExport(data) {
    // يمكن تخصيصها في المتحكمات الفرعية
    return data;
  }

  /**
   * إنشاء ملف التصدير
   */
  createExportFile(data, format) {
    try {
      if (format === 'csv') {
        return this.createCSVFile(data);
      } else if (format === 'excel') {
        return this.createExcelFile(data);
      }
      
      throw new Error('تنسيق التصدير غير مدعوم: ' + format);
    } catch (error) {
      Logger.log('خطأ في createExportFile: ' + error.toString());
      throw error;
    }
  }

  /**
   * إنشاء ملف CSV
   */
  createCSVFile(data) {
    if (!data || data.length === 0) {
      throw new Error('لا توجد بيانات للتصدير');
    }

    // الحصول على العناوين
    const headers = Object.keys(data[0]);
    let csvContent = headers.join(',') + '\n';

    // إضافة البيانات
    data.forEach(row => {
      const values = headers.map(header => {
        let value = row[header] || '';
        // تنظيف القيم للـ CSV
        if (typeof value === 'string') {
          value = value.replace(/"/g, '""');
          if (value.includes(',') || value.includes('\n') || value.includes('"')) {
            value = '"' + value + '"';
          }
        }
        return value;
      });
      csvContent += values.join(',') + '\n';
    });

    // إنشاء الملف
    const blob = Utilities.newBlob(csvContent, 'text/csv', `${this.moduleName}_export_${new Date().getTime()}.csv`);
    
    return {
      blob: blob,
      filename: blob.getName(),
      mimeType: 'text/csv'
    };
  }

  /**
   * إنشاء ملف Excel
   */
  createExcelFile(data) {
    // في هذا التنفيذ المبسط، نستخدم CSV
    // يمكن تطويره لاحقاً لدعم Excel الحقيقي
    const csvFile = this.createCSVFile(data);
    csvFile.filename = csvFile.filename.replace('.csv', '.xlsx');
    csvFile.mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    
    return csvFile;
  }

  /**
   * إرسال الإشعارات
   */
  sendNotification(action, data) {
    try {
      if (!CONFIG.NOTIFICATIONS.ENABLE_PUSH) {
        return;
      }

      const message = this.getNotificationMessage(action, data);
      if (message) {
        // يمكن تطوير نظام الإشعارات لاحقاً
        Logger.log(`إشعار: ${message}`);
      }
    } catch (error) {
      Logger.log('خطأ في sendNotification: ' + error.toString());
    }
  }

  /**
   * الحصول على رسالة الإشعار
   */
  getNotificationMessage(action, data) {
    const messages = {
      create: `تم إنشاء ${this.moduleName} جديد`,
      update: `تم تحديث ${this.moduleName}`,
      delete: `تم حذف ${this.moduleName}`
    };
    
    return messages[action] || null;
  }

  /**
   * الحصول على الإحصائيات
   */
  getStats(params = {}) {
    try {
      // التحقق من الصلاحيات
      if (!this.hasPermission('read')) {
        return { success: false, error: 'ليس لديك صلاحية لقراءة البيانات' };
      }

      const stats = {
        total: this.model.count(),
        active: this.model.count({ status: 'active' }),
        inactive: this.model.count({ status: 'inactive' }),
        thisMonth: this.model.count({
          created_at: {
            $gt: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        })
      };

      // إحصائيات مخصصة
      const customStats = this.getCustomStats(params);
      Object.assign(stats, customStats);

      return { success: true, data: stats };
    } catch (error) {
      Logger.log('خطأ في BaseController.getStats: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * الحصول على إحصائيات مخصصة
   */
  getCustomStats(params) {
    // يتم تخصيصها في المتحكمات الفرعية
    return {};
  }

  // دوال الأحداث (Events) - يمكن تخصيصها في المتحكمات الفرعية
  beforeCreate(data) { return data; }
  afterCreate(data) { }
  beforeUpdate(id, data) { return data; }
  afterUpdate(data) { }
  beforeDelete(id, record) { return { allowed: true }; }
  afterDelete(id, record) { }
  beforeList(criteria, options) { return criteria; }
  afterRead(record) { return record; }

  /**
   * التحقق من صحة المعرف
   */
  validateId(id) {
    if (!id || typeof id !== 'string' || id.trim() === '') {
      return { valid: false, error: 'معرف غير صحيح' };
    }
    return { valid: true };
  }

  /**
   * تنظيف البيانات المدخلة
   */
  sanitizeInput(data) {
    const sanitized = {};
    
    Object.keys(data).forEach(key => {
      let value = data[key];
      
      if (typeof value === 'string') {
        // إزالة المسافات الزائدة
        value = value.trim();
        
        // تنظيف HTML (أساسي)
        value = value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        value = value.replace(/<[^>]*>/g, '');
      }
      
      sanitized[key] = value;
    });
    
    return sanitized;
  }

  /**
   * معالجة الأخطاء
   */
  handleError(error, context = '') {
    const errorMessage = `خطأ في ${this.moduleName}Controller${context ? '.' + context : ''}: ${error.toString()}`;
    Logger.log(errorMessage);
    
    return {
      success: false,
      error: 'حدث خطأ في النظام، يرجى المحاولة مرة أخرى'
    };
  }
}
