/**
 * ملف الإعدادات العامة للنظام
 * Config.gs
 * يحتوي على جميع الإعدادات والثوابت المستخدمة في النظام
 */

/**
 * الإعدادات العامة للنظام
 */
const CONFIG = {
  // معلومات النظام
  SYSTEM: {
    NAME: 'نظام ERP المتكامل',
    VERSION: '1.0.0',
    AUTHOR: 'فريق التطوير',
    DESCRIPTION: 'نظام إدارة موارد المؤسسة الشامل',
    LANGUAGE: 'ar',
    DIRECTION: 'rtl'
  },

  // إعدادات قاعدة البيانات
  DATABASE: {
    SPREADSHEET_NAME: 'ERP_System_Database',
    BACKUP_FOLDER: 'ERP_Backups',
    AUTO_BACKUP: true,
    BACKUP_INTERVAL: 24, // ساعة
    MAX_RECORDS_PER_SHEET: 50000
  },

  // إعدادات المصادقة والأمان
  AUTH: {
    SESSION_TIMEOUT: 480, // دقيقة (8 ساعات)
    MAX_LOGIN_ATTEMPTS: 5,
    PASSWORD_MIN_LENGTH: 8,
    REQUIRE_STRONG_PASSWORD: true,
    ENABLE_2FA: false
  },

  // قائمة الموديولات المتاحة
  MODULES: [
    {
      name: 'CRM',
      title: 'إدارة العملاء',
      icon: 'fas fa-users',
      description: 'إدارة بيانات العملاء والعلاقات',
      enabled: true,
      order: 1
    },
    {
      name: 'Sales',
      title: 'المبيعات ونقاط البيع',
      icon: 'fas fa-shopping-cart',
      description: 'إدارة المبيعات وعمليات نقاط البيع',
      enabled: true,
      order: 2
    },
    {
      name: 'Products',
      title: 'المنتجات',
      icon: 'fas fa-box',
      description: 'إدارة كتالوج المنتجات والخدمات',
      enabled: true,
      order: 3
    },
    {
      name: 'Inventory',
      title: 'المخازن',
      icon: 'fas fa-warehouse',
      description: 'إدارة المخزون والمستودعات',
      enabled: true,
      order: 4
    },
    {
      name: 'Purchases',
      title: 'المشتريات',
      icon: 'fas fa-shopping-bag',
      description: 'إدارة عمليات الشراء والموردين',
      enabled: true,
      order: 5
    },
    {
      name: 'Manufacturing',
      title: 'التصنيع',
      icon: 'fas fa-industry',
      description: 'إدارة عمليات التصنيع والإنتاج',
      enabled: true,
      order: 6
    },
    {
      name: 'Treasury',
      title: 'الخزنة',
      icon: 'fas fa-cash-register',
      description: 'إدارة الخزنة والنقدية',
      enabled: true,
      order: 7
    },
    {
      name: 'Wallets',
      title: 'الفيزا والمحافظ البنكية',
      icon: 'fas fa-credit-card',
      description: 'إدارة المحافظ الإلكترونية والبنكية',
      enabled: true,
      order: 8
    },
    {
      name: 'Accounting',
      title: 'الحسابات',
      icon: 'fas fa-calculator',
      description: 'النظام المحاسبي والتقارير المالية',
      enabled: true,
      order: 9
    },
    {
      name: 'VanDistribution',
      title: 'توزيع البضائع',
      icon: 'fas fa-truck',
      description: 'إدارة توزيع البضائع والمندوبين',
      enabled: true,
      order: 10
    },
    {
      name: 'Settings',
      title: 'الإعدادات',
      icon: 'fas fa-cog',
      description: 'إعدادات النظام والصلاحيات',
      enabled: true,
      order: 11
    }
  ],

  // إعدادات واجهة المستخدم
  UI: {
    THEME: 'light',
    PRIMARY_COLOR: '#007bff',
    SECONDARY_COLOR: '#6c757d',
    SUCCESS_COLOR: '#28a745',
    DANGER_COLOR: '#dc3545',
    WARNING_COLOR: '#ffc107',
    INFO_COLOR: '#17a2b8',
    SIDEBAR_COLLAPSED: false,
    SHOW_BREADCRUMB: true,
    ITEMS_PER_PAGE: 25,
    DATE_FORMAT: 'DD/MM/YYYY',
    TIME_FORMAT: 'HH:mm:ss',
    CURRENCY: 'ج.م',
    DECIMAL_PLACES: 2
  },

  // إعدادات التوزيع الجغرافي
  GEO: {
    DEFAULT_LOCATION: {
      lat: 30.0444,
      lng: 31.2357 // القاهرة
    },
    MAX_DISTANCE_KM: 1, // المسافة القصوى للتحقق من الموقع
    ENABLE_GEOFENCING: true,
    TRACK_ROUTES: true,
    AUTO_LOCATION_UPDATE: 30000, // ميلي ثانية (30 ثانية)
    MAP_ZOOM_LEVEL: 15
  },

  // إعدادات التقارير
  REPORTS: {
    DEFAULT_PERIOD: 'month',
    EXPORT_FORMATS: ['pdf', 'excel', 'csv'],
    AUTO_GENERATE: false,
    EMAIL_REPORTS: false,
    CHART_COLORS: [
      '#007bff', '#28a745', '#ffc107', '#dc3545',
      '#17a2b8', '#6f42c1', '#e83e8c', '#fd7e14'
    ]
  },

  // إعدادات الإشعارات
  NOTIFICATIONS: {
    ENABLE_EMAIL: true,
    ENABLE_SMS: false,
    ENABLE_PUSH: true,
    LOW_STOCK_THRESHOLD: 10,
    OVERDUE_PAYMENT_DAYS: 30,
    DAILY_SUMMARY: true
  },

  // إعدادات النسخ الاحتياطي
  BACKUP: {
    AUTO_BACKUP: true,
    BACKUP_FREQUENCY: 'daily', // daily, weekly, monthly
    RETENTION_DAYS: 30,
    BACKUP_LOCATION: 'drive', // drive, email
    COMPRESS_BACKUPS: true
  },

  // إعدادات الأداء
  PERFORMANCE: {
    CACHE_ENABLED: true,
    CACHE_DURATION: 300, // ثانية (5 دقائق)
    LAZY_LOADING: true,
    BATCH_SIZE: 100,
    MAX_CONCURRENT_REQUESTS: 5
  },

  // إعدادات التكامل
  INTEGRATIONS: {
    GOOGLE_MAPS_API: '',
    PAYMENT_GATEWAY: '',
    SMS_PROVIDER: '',
    EMAIL_SERVICE: 'gmail',
    EXTERNAL_APIs: {}
  },

  // الصلاحيات الافتراضية
  DEFAULT_PERMISSIONS: {
    ADMIN: {
      modules: '*',
      actions: ['create', 'read', 'update', 'delete', 'export', 'import']
    },
    MANAGER: {
      modules: ['CRM', 'Sales', 'Products', 'Inventory', 'Purchases', 'Reports'],
      actions: ['create', 'read', 'update', 'export']
    },
    USER: {
      modules: ['CRM', 'Sales', 'Products'],
      actions: ['read', 'update']
    },
    VIEWER: {
      modules: ['CRM', 'Sales', 'Products', 'Inventory'],
      actions: ['read']
    }
  },

  // رسائل النظام
  MESSAGES: {
    SUCCESS: {
      SAVE: 'تم الحفظ بنجاح',
      UPDATE: 'تم التحديث بنجاح',
      DELETE: 'تم الحذف بنجاح',
      LOGIN: 'تم تسجيل الدخول بنجاح',
      LOGOUT: 'تم تسجيل الخروج بنجاح'
    },
    ERROR: {
      SAVE: 'خطأ في الحفظ',
      UPDATE: 'خطأ في التحديث',
      DELETE: 'خطأ في الحذف',
      LOGIN: 'خطأ في تسجيل الدخول',
      PERMISSION: 'ليس لديك صلاحية لتنفيذ هذا الإجراء',
      NETWORK: 'خطأ في الاتصال بالشبكة',
      VALIDATION: 'بيانات غير صحيحة'
    },
    WARNING: {
      UNSAVED_CHANGES: 'لديك تغييرات غير محفوظة',
      DELETE_CONFIRM: 'هل أنت متأكد من الحذف؟',
      LOGOUT_CONFIRM: 'هل تريد تسجيل الخروج؟'
    },
    INFO: {
      LOADING: 'جاري التحميل...',
      SAVING: 'جاري الحفظ...',
      PROCESSING: 'جاري المعالجة...',
      NO_DATA: 'لا توجد بيانات'
    }
  },

  // إعدادات التحقق من صحة البيانات
  VALIDATION: {
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PHONE_REGEX: /^[\+]?[1-9][\d]{0,15}$/,
    REQUIRED_FIELDS: {
      CRM: ['name', 'email'],
      Products: ['name', 'price'],
      Sales: ['customer_id', 'total'],
      Purchases: ['supplier_id', 'total']
    }
  }
};

/**
 * دالة الحصول على إعداد معين
 */
function getConfig(path) {
  try {
    const keys = path.split('.');
    let value = CONFIG;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return null;
      }
    }
    
    return value;
  } catch (error) {
    Logger.log('خطأ في getConfig: ' + error.toString());
    return null;
  }
}

/**
 * دالة تحديث إعداد معين
 */
function setConfig(path, newValue) {
  try {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let target = CONFIG;
    
    for (const key of keys) {
      if (!target[key] || typeof target[key] !== 'object') {
        target[key] = {};
      }
      target = target[key];
    }
    
    target[lastKey] = newValue;
    
    // حفظ الإعدادات في Properties
    PropertiesService.getScriptProperties().setProperty('CONFIG_' + path, JSON.stringify(newValue));
    
    return true;
  } catch (error) {
    Logger.log('خطأ في setConfig: ' + error.toString());
    return false;
  }
}

/**
 * دالة تحميل الإعدادات المخصصة
 */
function loadCustomConfig() {
  try {
    const properties = PropertiesService.getScriptProperties().getProperties();
    
    Object.keys(properties).forEach(key => {
      if (key.startsWith('CONFIG_')) {
        const path = key.replace('CONFIG_', '');
        const value = JSON.parse(properties[key]);
        setConfig(path, value);
      }
    });
    
  } catch (error) {
    Logger.log('خطأ في loadCustomConfig: ' + error.toString());
  }
}

/**
 * دالة إعادة تعيين الإعدادات للقيم الافتراضية
 */
function resetConfig() {
  try {
    const properties = PropertiesService.getScriptProperties();
    const allProperties = properties.getProperties();
    
    // حذف جميع الإعدادات المخصصة
    Object.keys(allProperties).forEach(key => {
      if (key.startsWith('CONFIG_')) {
        properties.deleteProperty(key);
      }
    });
    
    return true;
  } catch (error) {
    Logger.log('خطأ في resetConfig: ' + error.toString());
    return false;
  }
}
