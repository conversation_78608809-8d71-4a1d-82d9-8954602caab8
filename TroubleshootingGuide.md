# دليل استكشاف الأخطاء وإصلاحها - نظام ERP

## 🚨 مشكلة نفاد الذاكرة أثناء التثبيت

### الأعراض:
- رسالة خطأ: "حدث خطأ نظرًا لنفاد الذاكرة"
- توقف التثبيت في منتصف العملية
- بطء شديد في الاستجابة

### الحلول:

#### 1. استخدام المثبت المبسط (موصى به)
```javascript
// في ملف Code.gs، تم تحديث handleAjaxRequest لاستخدام:
simpleInstallSystem(adminData)
```

#### 2. تنظيف المشروع قبل التثبيت
1. احذف جميع الملفات غير المستخدمة
2. تأكد من عدم وجود ملفات كبيرة
3. امسح سجل التنفيذ (Execution Log)

#### 3. تقسيم التثبيت إلى مراحل
```javascript
// المرحلة 1: الجداول الأساسية
simpleInstallSystem(adminData)

// المرحلة 2: الجداول الإضافية (بعد التثبيت)
addRemainingTables()
```

## 🔧 مشاكل التثبيت الشائعة

### 1. خطأ في إنشاء قاعدة البيانات
**الحل:**
- تأكد من صلاحيات Google Drive
- تحقق من مساحة التخزين المتاحة
- جرب إنشاء جدول بيانات يدوياً أولاً

### 2. فشل في إنشاء المستخدم الرئيسي
**الحل:**
- تأكد من صحة البريد الإلكتروني
- استخدم كلمة مرور بسيطة (6 أحرف على الأقل)
- تحقق من عدم وجود مستخدم بنفس البريد

### 3. مشاكل الصلاحيات
**الحل:**
- تأكد من تفعيل Google Apps Script API
- امنح الصلاحيات المطلوبة للمشروع
- جرب تشغيل المشروع من محرر Apps Script مباشرة

## 🛠️ خطوات استكشاف الأخطاء

### 1. فحص السجلات
```javascript
// في محرر Apps Script
function checkLogs() {
  console.log('فحص السجلات...');
  // تحقق من Execution Log
}
```

### 2. اختبار الاتصال بقاعدة البيانات
```javascript
function testDatabaseConnection() {
  try {
    const db = getDatabase();
    console.log('الاتصال بقاعدة البيانات: نجح');
    return true;
  } catch (error) {
    console.log('خطأ في الاتصال: ' + error.toString());
    return false;
  }
}
```

### 3. اختبار التثبيت المبسط
```javascript
function testSimpleInstall() {
  const testData = {
    name: 'مدير تجريبي',
    email: '<EMAIL>',
    password: 'test123'
  };
  
  return simpleInstallSystem(testData);
}
```

## 📋 قائمة التحقق قبل التثبيت

### متطلبات النظام:
- [ ] حساب Google صالح
- [ ] صلاحيات Google Drive
- [ ] صلاحيات Google Sheets
- [ ] مساحة تخزين كافية (50 MB على الأقل)

### إعدادات المشروع:
- [ ] تفعيل Google Apps Script API
- [ ] تفعيل Google Drive API
- [ ] تفعيل Google Sheets API
- [ ] إعدادات الأمان مناسبة

### بيانات التثبيت:
- [ ] بريد إلكتروني صحيح
- [ ] كلمة مرور قوية (6+ أحرف)
- [ ] اسم المدير
- [ ] اسم الشركة (اختياري)

## 🔄 إعادة التثبيت

### في حالة فشل التثبيت:

1. **مسح البيانات السابقة:**
```javascript
function cleanupFailedInstallation() {
  // حذف قاعدة البيانات
  const files = DriveApp.getFilesByName('ERP_System_Database');
  while (files.hasNext()) {
    files.next().setTrashed(true);
  }
  
  // مسح الإعدادات
  PropertiesService.getScriptProperties().deleteProperty('system_installed');
  PropertiesService.getUserProperties().deleteAll();
}
```

2. **إعادة التثبيت:**
```javascript
// تشغيل التثبيت المبسط مرة أخرى
testSimpleInstall()
```

## 🚀 تحسين الأداء

### 1. تقليل استهلاك الذاكرة:
- استخدم `SIMPLE_CONFIG` بدلاً من `CONFIG`
- قلل عدد الموديولات المفعلة
- استخدم `cleanMemory()` بانتظام

### 2. تحسين قاعدة البيانات:
- قلل عدد الأعمدة في الجداول
- استخدم فهرسة بسيطة
- تجنب البيانات المكررة

### 3. تحسين الواجهة:
- قلل عدد العناصر في الصفحة
- استخدم التحميل التدريجي
- فعل التخزين المؤقت

## 📞 الحصول على المساعدة

### معلومات مفيدة عند طلب المساعدة:
1. رسالة الخطأ الكاملة
2. خطوات إعادة إنتاج المشكلة
3. إعدادات المتصفح والنظام
4. سجل التنفيذ من Apps Script

### أدوات التشخيص:
```javascript
// تشغيل هذه الدالة للحصول على معلومات التشخيص
function getDiagnosticInfo() {
  return {
    systemInstalled: isSystemInstalled(),
    databaseExists: testDatabaseConnection(),
    memoryUsage: getMemoryUsageStats(),
    enabledModules: getEnabledModules(),
    userAgent: Session.getTemporaryActiveUserKey()
  };
}
```

## 🔐 مشاكل الأمان

### 1. مشاكل تسجيل الدخول:
- تحقق من صحة البريد وكلمة المرور
- امسح ذاكرة التخزين المؤقت للمتصفح
- تأكد من تفعيل JavaScript

### 2. مشاكل الصلاحيات:
- تحقق من دور المستخدم
- راجع جدول الصلاحيات
- تأكد من وجود صلاحيات للموديول المطلوب

## 📊 مراقبة النظام

### مؤشرات الأداء:
- وقت الاستجابة
- استهلاك الذاكرة
- عدد الأخطاء
- عدد المستخدمين النشطين

### سجلات مهمة:
- سجل تسجيل الدخول
- سجل العمليات
- سجل الأخطاء
- سجل الأداء

---

## 💡 نصائح إضافية

1. **النسخ الاحتياطية:** احتفظ بنسخة احتياطية من قاعدة البيانات
2. **التحديثات:** تابع التحديثات والإصلاحات
3. **الاختبار:** اختبر النظام بانتظام
4. **المراقبة:** راقب الأداء والأخطاء
5. **التوثيق:** وثق أي تغييرات تقوم بها

---

*آخر تحديث: نوفمبر 2024*
