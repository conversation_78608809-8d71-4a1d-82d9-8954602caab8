/**
 * نظام المصادقة والصلاحيات
 * Auth.gs
 * يحتوي على جميع العمليات المتعلقة بالمصادقة وإدارة الصلاحيات
 */

/**
 * فئة إدارة المصادقة
 */
class AuthManager {
  constructor() {
    this.db = getDatabase();
    this.sessionTimeout = CONFIG.AUTH.SESSION_TIMEOUT * 60 * 1000; // تحويل إلى ميلي ثانية
  }

  /**
   * تسجيل دخول المستخدم
   */
  login(email, password) {
    try {
      // التحقق من محاولات تسجيل الدخول
      if (this.isAccountLocked(email)) {
        return { 
          success: false, 
          error: 'الحساب مقفل مؤقتاً بسبب محاولات دخول خاطئة متعددة' 
        };
      }

      // البحث عن المستخدم
      const user = this.findUserByEmail(email);
      if (!user) {
        this.recordFailedLogin(email);
        return { success: false, error: 'بيانات الدخول غير صحيحة' };
      }

      // التحقق من كلمة المرور
      const hashedPassword = this.hashPassword(password);
      if (user.password_hash !== hashedPassword) {
        this.recordFailedLogin(email);
        return { success: false, error: 'بيانات الدخول غير صحيحة' };
      }

      // التحقق من حالة الحساب
      if (user.status !== 'active') {
        return { success: false, error: 'الحساب غير نشط' };
      }

      // إنشاء جلسة جديدة
      const session = this.createSession(user);
      
      // تحديث آخر تسجيل دخول
      this.updateLastLogin(user.id);
      
      // مسح محاولات الدخول الفاشلة
      this.clearFailedLogins(email);

      return { 
        success: true, 
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        },
        session: session
      };

    } catch (error) {
      Logger.log('خطأ في login: ' + error.toString());
      return { success: false, error: 'خطأ في النظام' };
    }
  }

  /**
   * تسجيل خروج المستخدم
   */
  logout() {
    try {
      PropertiesService.getUserProperties().deleteProperty('current_user');
      PropertiesService.getUserProperties().deleteProperty('session_token');
      PropertiesService.getUserProperties().deleteProperty('session_expires');
      
      return { success: true };
    } catch (error) {
      Logger.log('خطأ في logout: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * الحصول على المستخدم الحالي
   */
  getCurrentUser() {
    try {
      const userProperties = PropertiesService.getUserProperties();
      const currentUser = userProperties.getProperty('current_user');
      const sessionExpires = userProperties.getProperty('session_expires');
      
      if (!currentUser || !sessionExpires) {
        return null;
      }

      // التحقق من انتهاء الجلسة
      if (new Date().getTime() > parseInt(sessionExpires)) {
        this.logout();
        return null;
      }

      return JSON.parse(currentUser);
    } catch (error) {
      Logger.log('خطأ في getCurrentUser: ' + error.toString());
      return null;
    }
  }

  /**
   * إنشاء جلسة جديدة
   */
  createSession(user) {
    try {
      const sessionToken = this.generateSessionToken();
      const expiresAt = new Date().getTime() + this.sessionTimeout;
      
      const userProperties = PropertiesService.getUserProperties();
      userProperties.setProperty('current_user', JSON.stringify(user));
      userProperties.setProperty('session_token', sessionToken);
      userProperties.setProperty('session_expires', expiresAt.toString());
      
      return {
        token: sessionToken,
        expires: new Date(expiresAt)
      };
    } catch (error) {
      Logger.log('خطأ في createSession: ' + error.toString());
      throw error;
    }
  }

  /**
   * توليد رمز الجلسة
   */
  generateSessionToken() {
    return Utilities.getUuid();
  }

  /**
   * البحث عن مستخدم بالبريد الإلكتروني
   */
  findUserByEmail(email) {
    try {
      const sheet = this.db.spreadsheet.getSheetByName('users');
      if (!sheet) return null;
      
      const data = sheet.getDataRange().getValues();
      const headers = data[0];
      
      for (let i = 1; i < data.length; i++) {
        const row = data[i];
        const user = {};
        
        headers.forEach((header, index) => {
          user[header] = row[index];
        });
        
        if (user.email === email) {
          return user;
        }
      }
      
      return null;
    } catch (error) {
      Logger.log('خطأ في findUserByEmail: ' + error.toString());
      return null;
    }
  }

  /**
   * تشفير كلمة المرور
   */
  hashPassword(password) {
    return Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_256, password)
      .map(byte => (byte + 256).toString(16).slice(-2))
      .join('');
  }

  /**
   * تسجيل محاولة دخول فاشلة
   */
  recordFailedLogin(email) {
    try {
      const properties = PropertiesService.getScriptProperties();
      const key = 'failed_login_' + email;
      const attempts = parseInt(properties.getProperty(key) || '0') + 1;
      
      properties.setProperty(key, attempts.toString());
      properties.setProperty(key + '_time', new Date().getTime().toString());
      
    } catch (error) {
      Logger.log('خطأ في recordFailedLogin: ' + error.toString());
    }
  }

  /**
   * التحقق من قفل الحساب
   */
  isAccountLocked(email) {
    try {
      const properties = PropertiesService.getScriptProperties();
      const key = 'failed_login_' + email;
      const attempts = parseInt(properties.getProperty(key) || '0');
      const lastAttemptTime = parseInt(properties.getProperty(key + '_time') || '0');
      
      if (attempts >= CONFIG.AUTH.MAX_LOGIN_ATTEMPTS) {
        const lockDuration = 30 * 60 * 1000; // 30 دقيقة
        const timeSinceLastAttempt = new Date().getTime() - lastAttemptTime;
        
        if (timeSinceLastAttempt < lockDuration) {
          return true;
        } else {
          // إعادة تعيين العداد بعد انتهاء فترة القفل
          this.clearFailedLogins(email);
        }
      }
      
      return false;
    } catch (error) {
      Logger.log('خطأ في isAccountLocked: ' + error.toString());
      return false;
    }
  }

  /**
   * مسح محاولات الدخول الفاشلة
   */
  clearFailedLogins(email) {
    try {
      const properties = PropertiesService.getScriptProperties();
      const key = 'failed_login_' + email;
      
      properties.deleteProperty(key);
      properties.deleteProperty(key + '_time');
      
    } catch (error) {
      Logger.log('خطأ في clearFailedLogins: ' + error.toString());
    }
  }

  /**
   * تحديث آخر تسجيل دخول
   */
  updateLastLogin(userId) {
    try {
      this.db.update('users', userId, { last_login: new Date() });
    } catch (error) {
      Logger.log('خطأ في updateLastLogin: ' + error.toString());
    }
  }

  /**
   * إنشاء مستخدم جديد
   */
  createUser(userData) {
    try {
      // التحقق من وجود المستخدم
      if (this.findUserByEmail(userData.email)) {
        return { success: false, error: 'المستخدم موجود بالفعل' };
      }

      // تشفير كلمة المرور
      userData.password_hash = this.hashPassword(userData.password);
      delete userData.password;

      // إضافة البيانات الافتراضية
      userData.status = userData.status || 'active';
      userData.role = userData.role || 'USER';

      // إدراج المستخدم
      const result = this.db.insert('users', userData);
      
      if (result.success) {
        // إنشاء الصلاحيات الافتراضية
        this.createDefaultPermissions(userData.email, userData.role);
      }

      return result;
    } catch (error) {
      Logger.log('خطأ في createUser: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }

  /**
   * إنشاء الصلاحيات الافتراضية
   */
  createDefaultPermissions(userEmail, role) {
    try {
      const defaultPerms = CONFIG.DEFAULT_PERMISSIONS[role];
      if (!defaultPerms) return;

      const modules = defaultPerms.modules === '*' ? 
        CONFIG.MODULES.map(m => m.name) : defaultPerms.modules;

      modules.forEach(moduleName => {
        const permissionData = {
          user_email: userEmail,
          module: moduleName,
          create: defaultPerms.actions.includes('create'),
          read: defaultPerms.actions.includes('read'),
          update: defaultPerms.actions.includes('update'),
          delete: defaultPerms.actions.includes('delete'),
          export: defaultPerms.actions.includes('export'),
          import: defaultPerms.actions.includes('import')
        };

        this.db.insert('permissions', permissionData);
      });

    } catch (error) {
      Logger.log('خطأ في createDefaultPermissions: ' + error.toString());
    }
  }

  /**
   * الحصول على صلاحيات المستخدم
   */
  getUserPermissions(userEmail) {
    try {
      const sheet = this.db.spreadsheet.getSheetByName('permissions');
      if (!sheet) return [];

      const data = sheet.getDataRange().getValues();
      const headers = data[0];
      const permissions = [];

      for (let i = 1; i < data.length; i++) {
        const row = data[i];
        const permission = {};
        
        headers.forEach((header, index) => {
          permission[header] = row[index];
        });

        if (permission.user_email === userEmail) {
          permissions.push(permission);
        }
      }

      return permissions;
    } catch (error) {
      Logger.log('خطأ في getUserPermissions: ' + error.toString());
      return [];
    }
  }

  /**
   * التحقق من صلاحية معينة
   */
  hasPermission(user, module, action) {
    try {
      if (!user) return false;
      
      // المدير له صلاحية كاملة
      if (user.role === 'ADMIN') return true;

      const permissions = this.getUserPermissions(user.email);
      const modulePermission = permissions.find(p => p.module === module);

      if (!modulePermission) return false;

      return modulePermission[action] === true;
    } catch (error) {
      Logger.log('خطأ في hasPermission: ' + error.toString());
      return false;
    }
  }

  /**
   * تحديث صلاحيات المستخدم
   */
  updateUserPermissions(userEmail, module, permissions) {
    try {
      const sheet = this.db.spreadsheet.getSheetByName('permissions');
      if (!sheet) return { success: false, error: 'جدول الصلاحيات غير موجود' };

      // البحث عن الصلاحية الموجودة
      const data = sheet.getDataRange().getValues();
      const headers = data[0];
      
      for (let i = 1; i < data.length; i++) {
        const row = data[i];
        const permission = {};
        
        headers.forEach((header, index) => {
          permission[header] = row[index];
        });

        if (permission.user_email === userEmail && permission.module === module) {
          // تحديث الصلاحيات
          Object.keys(permissions).forEach(key => {
            const colIndex = headers.indexOf(key);
            if (colIndex !== -1) {
              sheet.getRange(i + 1, colIndex + 1).setValue(permissions[key]);
            }
          });
          
          return { success: true };
        }
      }

      // إنشاء صلاحية جديدة إذا لم توجد
      const newPermission = {
        user_email: userEmail,
        module: module,
        ...permissions
      };

      return this.db.insert('permissions', newPermission);
    } catch (error) {
      Logger.log('خطأ في updateUserPermissions: ' + error.toString());
      return { success: false, error: error.toString() };
    }
  }
}

// الدوال العامة للمصادقة
const authManager = new AuthManager();

function login(email, password) {
  return authManager.login(email, password);
}

function logout() {
  return authManager.logout();
}

function getCurrentUser() {
  return authManager.getCurrentUser();
}

function hasPermission(user, module, action) {
  return authManager.hasPermission(user, module, action);
}

function getUserPermissions(userEmail) {
  return authManager.getUserPermissions(userEmail);
}

function createUser(userData) {
  return authManager.createUser(userData);
}

function updateUserPermissions(userEmail, module, permissions) {
  return authManager.updateUserPermissions(userEmail, module, permissions);
}

/**
 * التحقق من تثبيت النظام
 */
function isSystemInstalled() {
  try {
    const properties = PropertiesService.getScriptProperties();
    return properties.getProperty('system_installed') === 'true';
  } catch (error) {
    Logger.log('خطأ في isSystemInstalled: ' + error.toString());
    return false;
  }
}

/**
 * تعيين النظام كمثبت
 */
function markSystemAsInstalled() {
  try {
    PropertiesService.getScriptProperties().setProperty('system_installed', 'true');
    return true;
  } catch (error) {
    Logger.log('خطأ في markSystemAsInstalled: ' + error.toString());
    return false;
  }
}

/**
 * اختبار نظام الصلاحيات
 */
function testPermissions() {
  try {
    const testUser = { email: '<EMAIL>', role: 'USER' };
    const hasReadPermission = hasPermission(testUser, 'CRM', 'read');
    const hasDeletePermission = hasPermission(testUser, 'CRM', 'delete');
    
    return {
      userPermissions: hasReadPermission,
      adminPermissions: hasDeletePermission,
      permissionsTable: !!authManager.db.spreadsheet.getSheetByName('permissions')
    };
  } catch (error) {
    Logger.log('خطأ في testPermissions: ' + error.toString());
    return { error: error.toString() };
  }
}
