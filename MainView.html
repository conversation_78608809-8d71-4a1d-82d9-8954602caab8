<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام ERP المتكامل</title>
    <base target="_top">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --sidebar-width: 280px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            transform: translateX(100%);
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 600;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .menu-item {
            display: block;
            padding: 0.75rem 1.5rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
        }

        .menu-item:hover,
        .menu-item.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }

        .menu-item i {
            width: 20px;
            margin-left: 10px;
        }

        /* Main Content */
        .main-content {
            margin-right: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-right 0.3s ease;
        }

        .main-content.expanded {
            margin-right: 0;
        }

        /* Header */
        .header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #666;
            cursor: pointer;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        /* Content Area */
        .content-area {
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0.5rem 0 0 0;
        }

        /* Dashboard Cards */
        .dashboard-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            height: 100%;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .card-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        /* Loading Spinner */
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .header {
                padding: 1rem;
            }
            
            .content-area {
                padding: 1rem;
            }
        }

        /* Utility Classes */
        .text-primary { color: var(--primary-color) !important; }
        .bg-primary { background-color: var(--primary-color) !important; }
        .text-success { color: var(--success-color) !important; }
        .bg-success { background-color: var(--success-color) !important; }
        .text-danger { color: var(--danger-color) !important; }
        .bg-danger { background-color: var(--danger-color) !important; }
        .text-warning { color: var(--warning-color) !important; }
        .bg-warning { background-color: var(--warning-color) !important; }
        .text-info { color: var(--info-color) !important; }
        .bg-info { background-color: var(--info-color) !important; }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-chart-line me-2"></i>نظام ERP</h4>
            <small>الإصدار 1.0.0</small>
        </div>
        
        <div class="sidebar-menu" id="sidebarMenu">
            <!-- سيتم تحميل القائمة ديناميكياً -->
        </div>
        
        <div class="mt-auto p-3">
            <button class="btn btn-outline-light btn-sm w-100" onclick="logout()">
                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0" id="currentPageTitle">الصفحة الرئيسية</h5>
            </div>
            
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">
                    <i class="fas fa-user"></i>
                </div>
                <div>
                    <div class="fw-bold" id="userName">مستخدم</div>
                    <small class="text-muted" id="userRole">مستخدم</small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title" id="pageTitle">لوحة التحكم</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb" id="breadcrumb">
                        <li class="breadcrumb-item active">الرئيسية</li>
                    </ol>
                </nav>
            </div>

            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل البيانات...</p>
            </div>

            <!-- Dynamic Content -->
            <div id="dynamicContent">
                <!-- Dashboard Content -->
                <div class="row" id="dashboardContent">
                    <!-- سيتم تحميل المحتوى ديناميكياً -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // Global Variables
        let currentUser = null;
        let availableModules = [];
        let currentModule = null;
        let sidebarCollapsed = false;

        // Initialize Application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        // Initialize Application
        function initializeApp() {
            showLoading();
            
            // Load user data
            google.script.run
                .withSuccessHandler(handleUserDataLoaded)
                .withFailureHandler(handleError)
                .getCurrentUserData();
        }

        // Handle user data loaded
        function handleUserDataLoaded(userData) {
            if (!userData) {
                window.location.reload();
                return;
            }
            
            currentUser = userData;
            updateUserInfo();
            
            // Load available modules
            google.script.run
                .withSuccessHandler(handleModulesLoaded)
                .withFailureHandler(handleError)
                .getAvailableModules();
        }

        // Handle modules loaded
        function handleModulesLoaded(modules) {
            availableModules = modules;
            buildSidebarMenu();
            loadDashboard();
            hideLoading();
        }

        // Update user info in header
        function updateUserInfo() {
            document.getElementById('userName').textContent = currentUser.name;
            document.getElementById('userRole').textContent = currentUser.role;
            
            // Set user avatar
            const avatar = document.getElementById('userAvatar');
            avatar.textContent = currentUser.name.charAt(0).toUpperCase();
        }

        // Build sidebar menu
        function buildSidebarMenu() {
            const menu = document.getElementById('sidebarMenu');
            menu.innerHTML = '';
            
            // Dashboard link
            const dashboardItem = createMenuItem('dashboard', 'لوحة التحكم', 'fas fa-tachometer-alt', true);
            menu.appendChild(dashboardItem);
            
            // Module links
            availableModules.forEach(module => {
                const menuItem = createMenuItem(module.name, module.title, module.icon, false);
                menu.appendChild(menuItem);
            });
        }

        // Create menu item
        function createMenuItem(id, title, icon, active = false) {
            const item = document.createElement('button');
            item.className = `menu-item ${active ? 'active' : ''}`;
            item.innerHTML = `<i class="${icon}"></i>${title}`;
            item.onclick = () => loadModule(id, title);
            item.setAttribute('data-module', id);
            return item;
        }

        // Load module
        function loadModule(moduleId, moduleTitle) {
            // Update active menu item
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-module="${moduleId}"]`).classList.add('active');
            
            // Update page title
            updatePageTitle(moduleTitle);
            
            if (moduleId === 'dashboard') {
                loadDashboard();
            } else {
                loadModuleView(moduleId);
            }
            
            // Hide sidebar on mobile
            if (window.innerWidth <= 768) {
                toggleSidebar();
            }
        }

        // Load dashboard
        function loadDashboard() {
            currentModule = null;
            updatePageTitle('لوحة التحكم');
            updateBreadcrumb(['الرئيسية']);
            
            showLoading();
            
            // Load dashboard data
            const dashboardHtml = `
                <div class="row g-4">
                    <div class="col-md-3 col-sm-6">
                        <div class="dashboard-card">
                            <div class="card-icon bg-primary">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-title">العملاء</div>
                            <div class="card-value" id="customersCount">-</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="dashboard-card">
                            <div class="card-icon bg-success">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="card-title">المبيعات</div>
                            <div class="card-value" id="salesCount">-</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="dashboard-card">
                            <div class="card-icon bg-warning">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="card-title">المنتجات</div>
                            <div class="card-value" id="productsCount">-</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="dashboard-card">
                            <div class="card-icon bg-info">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <div class="card-title">المخزون</div>
                            <div class="card-value" id="inventoryCount">-</div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="dashboard-card">
                            <h5>مرحباً بك في نظام ERP المتكامل</h5>
                            <p class="text-muted">يمكنك الآن إدارة جميع عمليات مؤسستك من مكان واحد</p>
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <h6><i class="fas fa-check-circle text-success me-2"></i>إدارة العملاء</h6>
                                    <p class="small text-muted">تتبع وإدارة بيانات العملاء والعلاقات</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-check-circle text-success me-2"></i>نظام المبيعات</h6>
                                    <p class="small text-muted">إدارة المبيعات ونقاط البيع</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-check-circle text-success me-2"></i>إدارة المخزون</h6>
                                    <p class="small text-muted">تتبع المنتجات والمخزون</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('dynamicContent').innerHTML = dashboardHtml;
            hideLoading();
            
            // Load dashboard statistics
            loadDashboardStats();
        }

        // Load dashboard statistics
        function loadDashboardStats() {
            // This would typically load real statistics
            // For now, we'll show placeholder values
            setTimeout(() => {
                document.getElementById('customersCount').textContent = '0';
                document.getElementById('salesCount').textContent = '0';
                document.getElementById('productsCount').textContent = '0';
                document.getElementById('inventoryCount').textContent = '0';
            }, 500);
        }

        // Load module view
        function loadModuleView(moduleId) {
            currentModule = moduleId;
            showLoading();
            
            google.script.run
                .withSuccessHandler(handleModuleViewLoaded)
                .withFailureHandler(handleError)
                .loadPage(moduleId);
        }

        // Handle module view loaded
        function handleModuleViewLoaded(html) {
            document.getElementById('dynamicContent').innerHTML = html;
            hideLoading();
        }

        // Update page title
        function updatePageTitle(title) {
            document.getElementById('currentPageTitle').textContent = title;
            document.getElementById('pageTitle').textContent = title;
        }

        // Update breadcrumb
        function updateBreadcrumb(items) {
            const breadcrumb = document.getElementById('breadcrumb');
            breadcrumb.innerHTML = '';
            
            items.forEach((item, index) => {
                const li = document.createElement('li');
                li.className = `breadcrumb-item ${index === items.length - 1 ? 'active' : ''}`;
                li.textContent = item;
                breadcrumb.appendChild(li);
            });
        }

        // Sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', toggleSidebar);

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
            
            sidebarCollapsed = !sidebarCollapsed;
        }

        // Logout function
        function logout() {
            Swal.fire({
                title: 'تسجيل الخروج',
                text: 'هل تريد تسجيل الخروج من النظام؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    google.script.run
                        .withSuccessHandler(() => {
                            window.location.reload();
                        })
                        .withFailureHandler(handleError)
                        .logout();
                }
            });
        }

        // Show loading
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('dynamicContent').style.display = 'none';
        }

        // Hide loading
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('dynamicContent').style.display = 'block';
        }

        // Handle errors
        function handleError(error) {
            console.error('Error:', error);
            hideLoading();
            
            Swal.fire({
                title: 'خطأ',
                text: 'حدث خطأ في النظام، يرجى المحاولة مرة أخرى',
                icon: 'error',
                confirmButtonText: 'موافق'
            });
        }

        // Responsive handling
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                document.getElementById('sidebar').classList.remove('show');
            }
        });
    </script>
</body>
</html>
