/**
 * إعدادات مبسطة للنظام
 * SimpleConfig.gs
 * نسخة مبسطة من الإعدادات لتقليل استهلاك الذاكرة
 */

/**
 * الإعدادات الأساسية المبسطة
 */
const SIMPLE_CONFIG = {
  // معلومات النظام
  SYSTEM: {
    NAME: 'نظام ERP المتكامل',
    VERSION: '1.0.0',
    LANGUAGE: 'ar',
    DIRECTION: 'rtl'
  },

  // إعدادات قاعدة البيانات
  DATABASE: {
    SPREADSHEET_NAME: 'ERP_System_Database',
    AUTO_BACKUP: false, // معطل لتوفير الذاكرة
    MAX_RECORDS_PER_SHEET: 10000 // مقلل لتوفير الذاكرة
  },

  // إعدادات المصادقة
  AUTH: {
    SESSION_TIMEOUT: 240, // 4 ساعات (مقلل)
    MAX_LOGIN_ATTEMPTS: 3, // مقلل
    PASSWORD_MIN_LENGTH: 6 // مقلل
  },

  // الموديولات الأساسية فقط
  MODULES: [
    {
      name: 'CRM',
      title: 'إدارة العملاء',
      icon: 'fas fa-users',
      enabled: true,
      order: 1
    },
    {
      name: 'Sales',
      title: 'المبيعات',
      icon: 'fas fa-shopping-cart',
      enabled: true,
      order: 2
    },
    {
      name: 'Products',
      title: 'المنتجات',
      icon: 'fas fa-box',
      enabled: true,
      order: 3
    },
    {
      name: 'Inventory',
      title: 'المخازن',
      icon: 'fas fa-warehouse',
      enabled: true,
      order: 4
    }
  ],

  // إعدادات واجهة المستخدم
  UI: {
    THEME: 'light',
    PRIMARY_COLOR: '#007bff',
    ITEMS_PER_PAGE: 20, // مقلل لتوفير الذاكرة
    DATE_FORMAT: 'DD/MM/YYYY',
    CURRENCY: 'ج.م'
  },

  // الصلاحيات الافتراضية المبسطة
  DEFAULT_PERMISSIONS: {
    ADMIN: {
      modules: ['CRM', 'Sales', 'Products', 'Inventory'],
      actions: ['create', 'read', 'update', 'delete']
    },
    USER: {
      modules: ['CRM', 'Sales'],
      actions: ['read', 'update']
    }
  },

  // رسائل النظام المبسطة
  MESSAGES: {
    SUCCESS: {
      SAVE: 'تم الحفظ بنجاح',
      UPDATE: 'تم التحديث بنجاح',
      DELETE: 'تم الحذف بنجاح',
      LOGIN: 'تم تسجيل الدخول بنجاح'
    },
    ERROR: {
      SAVE: 'خطأ في الحفظ',
      UPDATE: 'خطأ في التحديث',
      DELETE: 'خطأ في الحذف',
      LOGIN: 'خطأ في تسجيل الدخول',
      PERMISSION: 'ليس لديك صلاحية لتنفيذ هذا الإجراء',
      NETWORK: 'خطأ في الاتصال بالشبكة'
    }
  },

  // إعدادات التحقق من صحة البيانات
  VALIDATION: {
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PHONE_REGEX: /^[\+]?[1-9][\d]{0,15}$/,
    REQUIRED_FIELDS: {
      CRM: ['name', 'email'],
      Products: ['name', 'price'],
      Sales: ['customer_id', 'total']
    }
  }
};

/**
 * دالة الحصول على إعداد مبسط
 */
function getSimpleConfig(path) {
  try {
    const keys = path.split('.');
    let value = SIMPLE_CONFIG;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return null;
      }
    }
    
    return value;
  } catch (error) {
    Logger.log('خطأ في getSimpleConfig: ' + error.toString());
    return null;
  }
}

/**
 * دالة تحديث إعداد مبسط
 */
function setSimpleConfig(path, newValue) {
  try {
    // حفظ الإعدادات في Properties فقط
    PropertiesService.getScriptProperties().setProperty('SIMPLE_CONFIG_' + path, JSON.stringify(newValue));
    return true;
  } catch (error) {
    Logger.log('خطأ في setSimpleConfig: ' + error.toString());
    return false;
  }
}

/**
 * دالة تحميل الإعدادات المخصصة المبسطة
 */
function loadSimpleCustomConfig() {
  try {
    const properties = PropertiesService.getScriptProperties().getProperties();
    
    Object.keys(properties).forEach(key => {
      if (key.startsWith('SIMPLE_CONFIG_')) {
        const path = key.replace('SIMPLE_CONFIG_', '');
        try {
          const value = JSON.parse(properties[key]);
          // تطبيق القيمة على SIMPLE_CONFIG
          const keys = path.split('.');
          let target = SIMPLE_CONFIG;
          for (let i = 0; i < keys.length - 1; i++) {
            if (!target[keys[i]]) target[keys[i]] = {};
            target = target[keys[i]];
          }
          target[keys[keys.length - 1]] = value;
        } catch (error) {
          Logger.log('خطأ في تحليل الإعداد: ' + key);
        }
      }
    });
    
  } catch (error) {
    Logger.log('خطأ في loadSimpleCustomConfig: ' + error.toString());
  }
}

/**
 * دالة إعادة تعيين الإعدادات المبسطة
 */
function resetSimpleConfig() {
  try {
    const properties = PropertiesService.getScriptProperties();
    const allProperties = properties.getProperties();
    
    // حذف جميع الإعدادات المخصصة
    Object.keys(allProperties).forEach(key => {
      if (key.startsWith('SIMPLE_CONFIG_')) {
        properties.deleteProperty(key);
      }
    });
    
    return true;
  } catch (error) {
    Logger.log('خطأ في resetSimpleConfig: ' + error.toString());
    return false;
  }
}

/**
 * دالة الحصول على الموديولات المفعلة
 */
function getEnabledModules() {
  try {
    return SIMPLE_CONFIG.MODULES.filter(module => module.enabled);
  } catch (error) {
    Logger.log('خطأ في getEnabledModules: ' + error.toString());
    return [];
  }
}

/**
 * دالة التحقق من وجود موديول
 */
function isModuleEnabled(moduleName) {
  try {
    const module = SIMPLE_CONFIG.MODULES.find(m => m.name === moduleName);
    return module && module.enabled;
  } catch (error) {
    Logger.log('خطأ في isModuleEnabled: ' + error.toString());
    return false;
  }
}

/**
 * دالة الحصول على الصلاحيات الافتراضية
 */
function getDefaultPermissions(role) {
  try {
    return SIMPLE_CONFIG.DEFAULT_PERMISSIONS[role] || SIMPLE_CONFIG.DEFAULT_PERMISSIONS.USER;
  } catch (error) {
    Logger.log('خطأ في getDefaultPermissions: ' + error.toString());
    return SIMPLE_CONFIG.DEFAULT_PERMISSIONS.USER;
  }
}

/**
 * دالة الحصول على رسالة النظام
 */
function getSystemMessage(type, key) {
  try {
    return SIMPLE_CONFIG.MESSAGES[type] && SIMPLE_CONFIG.MESSAGES[type][key] 
      ? SIMPLE_CONFIG.MESSAGES[type][key] 
      : 'رسالة غير معروفة';
  } catch (error) {
    Logger.log('خطأ في getSystemMessage: ' + error.toString());
    return 'رسالة غير معروفة';
  }
}

/**
 * دالة التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
  try {
    return SIMPLE_CONFIG.VALIDATION.EMAIL_REGEX.test(email);
  } catch (error) {
    Logger.log('خطأ في isValidEmail: ' + error.toString());
    return false;
  }
}

/**
 * دالة التحقق من صحة رقم الهاتف
 */
function isValidPhone(phone) {
  try {
    return SIMPLE_CONFIG.VALIDATION.PHONE_REGEX.test(phone);
  } catch (error) {
    Logger.log('خطأ في isValidPhone: ' + error.toString());
    return false;
  }
}

/**
 * دالة الحصول على الحقول المطلوبة لموديول
 */
function getRequiredFields(moduleName) {
  try {
    return SIMPLE_CONFIG.VALIDATION.REQUIRED_FIELDS[moduleName] || [];
  } catch (error) {
    Logger.log('خطأ في getRequiredFields: ' + error.toString());
    return [];
  }
}

/**
 * دالة تحميل الإعدادات عند بدء التشغيل
 */
function initializeSimpleConfig() {
  try {
    loadSimpleCustomConfig();
    Logger.log('تم تحميل الإعدادات المبسطة');
  } catch (error) {
    Logger.log('خطأ في initializeSimpleConfig: ' + error.toString());
  }
}

// تحميل الإعدادات عند تحميل الملف
initializeSimpleConfig();
